"""
MSNets训练器
"""
import os
import sys
import torch.nn as nn

# 添加路径以导入必要模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from other_train.trainers.base_trainer import BaseTrainer
from other_train.models.msnets import create_msnets
from other_train.config import get_config


class MSNetsTrainer(BaseTrainer):
    """MSNets训练器"""
    
    def __init__(self, config):
        super().__init__(config)
    
    def _create_model(self) -> nn.Module:
        """创建MSNets模型"""
        model_config = {
            'num_classes': self.config.num_classes,
            'scales': getattr(self.config, 'scales', [1.0, 0.875, 0.75]),
            'backbone_type': 'resnet50',  # 固定使用ResNet50作为backbone
            'fusion_method': getattr(self.config, 'fusion_method', 'concat'),
            'pretrained': getattr(self.config, 'pretrained', True)
        }
        
        return create_msnets(**model_config)
    
    def _freeze_backbone(self):
        """冻结MSNets的backbone"""
        if hasattr(self.model, 'scale_branch'):
            # 冻结共享的尺度分支backbone
            for param in self.model.scale_branch.backbone.parameters():
                param.requires_grad = False
            
            # 只训练特征投影层、融合层和分类器
            for param in self.model.scale_branch.feature_proj.parameters():
                param.requires_grad = True
            
            for param in self.model.feature_fusion.parameters():
                param.requires_grad = True
            
            for param in self.model.classifier.parameters():
                param.requires_grad = True
            
            print("已冻结MSNets backbone，只训练特征投影、融合和分类器")
        else:
            print("警告: 无法找到scale_branch，未执行冻结操作")


def train_msnets_aptos():
    """训练MSNets在APTOS数据集上"""
    print("=" * 60)
    print("开始训练 MSNets 在 APTOS 数据集上")
    print("=" * 60)
    
    # 获取配置
    config = get_config('aptos', 'msnets')
    
    # 创建训练器
    trainer = MSNetsTrainer(config)
    
    # 训练
    trainer.train()
    
    # 测试
    test_metrics = trainer.test()
    print(f"\n测试结果:")
    for metric, value in test_metrics.items():
        print(f"{metric.upper()}: {value:.4f}")
    
    # 保存结果
    trainer.save_training_metrics()
    
    return trainer


def train_msnets_odir():
    """训练MSNets在ODIR数据集上"""
    print("=" * 60)
    print("开始训练 MSNets 在 ODIR 数据集上")
    print("=" * 60)
    
    # 获取配置
    config = get_config('odir', 'msnets')
    
    # 创建训练器
    trainer = MSNetsTrainer(config)
    
    # 训练
    trainer.train()
    
    # 测试
    test_metrics = trainer.test()
    print(f"\n测试结果:")
    for metric, value in test_metrics.items():
        print(f"{metric.upper()}: {value:.4f}")
    
    # 保存结果
    trainer.save_training_metrics()
    
    return trainer


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='训练MSNets模型')
    parser.add_argument('--dataset', type=str, choices=['aptos', 'odir'], 
                       default='aptos', help='数据集选择')
    
    args = parser.parse_args()
    
    if args.dataset == 'aptos':
        trainer = train_msnets_aptos()
    elif args.dataset == 'odir':
        trainer = train_msnets_odir()
    
    print(f"\nMSNets在{args.dataset.upper()}数据集上的训练完成!")
