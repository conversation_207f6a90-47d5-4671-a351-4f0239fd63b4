"""
配置模块初始化文件
"""
from .base_config import BaseConfig, ModelConfig, create_base_config
from .aptos_config import (
    APTOSConfig, APTOSDenseNetConfig, APTOSEfficientNetConfig,
    APTOSViTConfig, APTOSSEResNetConfig, APTOSMSNetsConfig,
    create_aptos_config
)
from .odir_config import (
    ODIRConfig, ODIRDenseNetConfig, ODIREfficientNetConfig,
    ODIRViTConfig, ODIRSEResNetConfig, ODIRMSNetsConfig,
    create_odir_config
)

__all__ = [
    # 基础配置
    'BaseConfig', 'ModelConfig', 'create_base_config',
    
    # APTOS配置
    'APTOSConfig', 'APTOSDenseNetConfig', 'APTOSEfficientNetConfig',
    'APTOSViTConfig', 'APTOSSEResNetConfig', 'APTOSMSNetsConfig',
    'create_aptos_config',
    
    # ODIR配置
    'ODIRConfig', 'ODIRDenseNetConfig', 'ODIREfficientNetConfig',
    'ODIRViTConfig', 'ODIRSEResNetConfig', 'ODIRMSNetsConfig',
    'create_odir_config'
]


def get_config(dataset: str, model_type: str):
    """
    获取指定数据集和模型类型的配置
    
    Args:
        dataset (str): 数据集名称 ('aptos' 或 'odir')
        model_type (str): 模型类型 ('densenet', 'efficientnet', 'vit', 'se_resnet', 'msnets')
    
    Returns:
        配置对象
    """
    if dataset.lower() == 'aptos':
        return create_aptos_config(model_type)
    elif dataset.lower() == 'odir':
        return create_odir_config(model_type)
    else:
        raise ValueError(f"Unsupported dataset: {dataset}")


if __name__ == "__main__":
    # 测试配置获取
    print("测试配置模块...")
    
    # 测试APTOS DenseNet配置
    aptos_densenet_config = get_config('aptos', 'densenet')
    print(f"APTOS DenseNet配置: {aptos_densenet_config.model_name}")
    
    # 测试ODIR ViT配置
    odir_vit_config = get_config('odir', 'vit')
    print(f"ODIR ViT配置: {odir_vit_config.model_name}")
    
    print("配置模块测试完成!")
