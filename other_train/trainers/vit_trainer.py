"""
Vision Transformer训练器
"""
import os
import sys
import torch.nn as nn

# 添加路径以导入必要模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from other_train.trainers.base_trainer import BaseTrainer
from other_train.models.vit import create_vit
from other_train.config import get_config


class ViTTrainer(BaseTrainer):
    """Vision Transformer训练器"""
    
    def __init__(self, config):
        super().__init__(config)
    
    def _create_model(self) -> nn.Module:
        """创建ViT模型"""
        model_config = {
            'num_classes': self.config.num_classes,
            'pretrained': getattr(self.config, 'pretrained', True),
            'patch_size': getattr(self.config, 'patch_size', 16),
            'embed_dim': getattr(self.config, 'embed_dim', 768),
            'depth': getattr(self.config, 'depth', 12),
            'num_heads': getattr(self.config, 'num_heads', 12),
            'mlp_ratio': getattr(self.config, 'mlp_ratio', 4.0),
            'drop_rate': getattr(self.config, 'drop_rate', 0.1),
            'attn_drop_rate': getattr(self.config, 'attn_drop_rate', 0.0)
        }
        
        return create_vit(**model_config)
    
    def _freeze_backbone(self):
        """冻结ViT的backbone"""
        if hasattr(self.model, 'backbone'):
            # 如果使用预训练模型
            # 冻结除了分类头以外的所有参数
            for name, param in self.model.backbone.named_parameters():
                if 'heads' not in name:  # 不冻结分类头
                    param.requires_grad = False
            
            print("已冻结ViT backbone，只训练分类头")
        elif hasattr(self.model, 'blocks'):
            # 如果是从头构建的模型
            # 冻结patch embedding和transformer blocks
            for param in self.model.patch_embed.parameters():
                param.requires_grad = False
            
            for param in self.model.blocks.parameters():
                param.requires_grad = False
            
            # 冻结position embedding和class token
            self.model.pos_embed.requires_grad = False
            self.model.cls_token.requires_grad = False
            
            # 只训练分类头
            for param in self.model.head.parameters():
                param.requires_grad = True
            
            for param in self.model.norm.parameters():
                param.requires_grad = True
            
            print("已冻结ViT backbone，只训练分类头和LayerNorm")
        else:
            print("警告: 无法识别ViT模型结构，未执行冻结操作")


def train_vit_aptos():
    """训练ViT在APTOS数据集上"""
    print("=" * 60)
    print("开始训练 Vision Transformer 在 APTOS 数据集上")
    print("=" * 60)
    
    # 获取配置
    config = get_config('aptos', 'vit')
    
    # 创建训练器
    trainer = ViTTrainer(config)
    
    # 训练
    trainer.train()
    
    # 测试
    test_metrics = trainer.test()
    print(f"\n测试结果:")
    for metric, value in test_metrics.items():
        print(f"{metric.upper()}: {value:.4f}")
    
    # 保存结果
    trainer.save_training_metrics()
    
    return trainer


def train_vit_odir():
    """训练ViT在ODIR数据集上"""
    print("=" * 60)
    print("开始训练 Vision Transformer 在 ODIR 数据集上")
    print("=" * 60)
    
    # 获取配置
    config = get_config('odir', 'vit')
    
    # 创建训练器
    trainer = ViTTrainer(config)
    
    # 训练
    trainer.train()
    
    # 测试
    test_metrics = trainer.test()
    print(f"\n测试结果:")
    for metric, value in test_metrics.items():
        print(f"{metric.upper()}: {value:.4f}")
    
    # 保存结果
    trainer.save_training_metrics()
    
    return trainer


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='训练Vision Transformer模型')
    parser.add_argument('--dataset', type=str, choices=['aptos', 'odir'], 
                       default='aptos', help='数据集选择')
    
    args = parser.parse_args()
    
    if args.dataset == 'aptos':
        trainer = train_vit_aptos()
    elif args.dataset == 'odir':
        trainer = train_vit_odir()
    
    print(f"\nVision Transformer在{args.dataset.upper()}数据集上的训练完成!")
