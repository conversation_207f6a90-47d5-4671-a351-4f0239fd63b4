"""
SE-ResNet训练器
"""
import os
import sys
import torch.nn as nn

# 添加路径以导入必要模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from other_train.trainers.base_trainer import BaseTrainer
from other_train.models.se_resnet import create_se_resnet
from other_train.config import get_config


class SEResNetTrainer(BaseTrainer):
    """SE-ResNet训练器"""
    
    def __init__(self, config):
        super().__init__(config)
    
    def _create_model(self) -> nn.Module:
        """创建SE-ResNet模型"""
        model_config = {
            'num_classes': self.config.num_classes,
            'pretrained': getattr(self.config, 'pretrained', True),
            'layers': getattr(self.config, 'layers', [3, 4, 6, 3]),  # ResNet50
            'reduction': getattr(self.config, 'reduction', 16)
        }
        
        return create_se_resnet(**model_config)
    
    def _freeze_backbone(self):
        """冻结SE-ResNet的backbone"""
        if hasattr(self.model, 'backbone'):
            # 如果使用预训练模型
            # 冻结除了分类器以外的所有参数
            for name, param in self.model.backbone.named_parameters():
                if 'fc' not in name:  # 不冻结分类器
                    param.requires_grad = False
            
            print("已冻结SE-ResNet backbone，只训练分类器")
        else:
            # 如果是从头构建的模型
            # 冻结特征提取层
            layers_to_freeze = ['conv1', 'bn1', 'layer1', 'layer2', 'layer3', 'layer4']
            
            for layer_name in layers_to_freeze:
                if hasattr(self.model, layer_name):
                    layer = getattr(self.model, layer_name)
                    for param in layer.parameters():
                        param.requires_grad = False
            
            # 只训练分类器
            if hasattr(self.model, 'fc'):
                for param in self.model.fc.parameters():
                    param.requires_grad = True
            
            print("已冻结SE-ResNet backbone，只训练分类器")


def train_se_resnet_aptos():
    """训练SE-ResNet在APTOS数据集上"""
    print("=" * 60)
    print("开始训练 SE-ResNet 在 APTOS 数据集上")
    print("=" * 60)
    
    # 获取配置
    config = get_config('aptos', 'se_resnet')
    
    # 创建训练器
    trainer = SEResNetTrainer(config)
    
    # 训练
    trainer.train()
    
    # 测试
    test_metrics = trainer.test()
    print(f"\n测试结果:")
    for metric, value in test_metrics.items():
        print(f"{metric.upper()}: {value:.4f}")
    
    # 保存结果
    trainer.save_training_metrics()
    
    return trainer


def train_se_resnet_odir():
    """训练SE-ResNet在ODIR数据集上"""
    print("=" * 60)
    print("开始训练 SE-ResNet 在 ODIR 数据集上")
    print("=" * 60)
    
    # 获取配置
    config = get_config('odir', 'se_resnet')
    
    # 创建训练器
    trainer = SEResNetTrainer(config)
    
    # 训练
    trainer.train()
    
    # 测试
    test_metrics = trainer.test()
    print(f"\n测试结果:")
    for metric, value in test_metrics.items():
        print(f"{metric.upper()}: {value:.4f}")
    
    # 保存结果
    trainer.save_training_metrics()
    
    return trainer


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='训练SE-ResNet模型')
    parser.add_argument('--dataset', type=str, choices=['aptos', 'odir'], 
                       default='aptos', help='数据集选择')
    
    args = parser.parse_args()
    
    if args.dataset == 'aptos':
        trainer = train_se_resnet_aptos()
    elif args.dataset == 'odir':
        trainer = train_se_resnet_odir()
    
    print(f"\nSE-ResNet在{args.dataset.upper()}数据集上的训练完成!")
