"""
基础训练器类 - 确保所有对比模型使用相同的训练流程
"""
import os
import json
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from datetime import datetime
from typing import Dict, Tuple, Optional
from abc import ABC, abstractmethod

import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from dataloader_APTOS import APTOSDataLoader
from dataloader_ODIR import ODIRDataLoader
from other_train.config.base_config import BaseConfig
from other_train.utils.metrics import MetricsTracker, get_primary_metric


class BaseTrainer(ABC):
    """基础训练器抽象类"""
    
    def __init__(self, config: BaseConfig):
        self.config = config
        self.device = config.device
        
        # 初始化数据加载器
        self.train_loader, self.val_loader, self.test_loader = self._create_dataloaders()
        
        # 初始化模型
        self.model = self._create_model().to(self.device)
        
        # 初始化优化器、调度器和损失函数
        self.optimizer = config.get_optimizer(self.model.parameters())
        self.scheduler = config.get_scheduler(self.optimizer, config.epochs)
        self.criterion = config.get_criterion()
        
        # 初始化指标跟踪器
        self.metrics_tracker = MetricsTracker(
            dataset_type=config.dataset_name.lower(),
            num_classes=config.num_classes
        )
        
        # 训练历史记录
        self.training_history = {
            'train_loss': [],
            'val_accuracy': [],
            'val_precision': [],
            'val_recall': [],
            'val_f1': [],
            'val_auc': [],
            'learning_rate': [],
            'epochs': [],
            'best_epoch': 0,
            'best_metric': 0.0
        }
        
        # 添加数据集特定的指标
        if config.dataset_name.lower() == "aptos":
            self.training_history['val_qwk'] = []
        
        self.best_metric = 0.0
        self.test_results = None
        
        # 冻结backbone（如果配置要求）
        if hasattr(config, 'freeze_backbone') and config.freeze_backbone:
            self._freeze_backbone()
    
    @abstractmethod
    def _create_model(self) -> nn.Module:
        """创建模型 - 子类必须实现"""
        pass
    
    @abstractmethod
    def _freeze_backbone(self):
        """冻结backbone - 子类必须实现"""
        pass
    
    def _create_dataloaders(self) -> Tuple[DataLoader, DataLoader, DataLoader]:
        """创建数据加载器"""
        if self.config.dataset_name.lower() == "aptos":
            dataloader = APTOSDataLoader(
                data_dir=self.config.data_dir,
                batch_size=self.config.batch_size,
                num_workers=self.config.num_workers
            )
        elif self.config.dataset_name.lower() == "odir":
            dataloader = ODIRDataLoader(
                data_dir=self.config.data_dir,
                batch_size=self.config.batch_size,
                num_workers=self.config.num_workers
            )
        else:
            raise ValueError(f"Unsupported dataset: {self.config.dataset_name}")
        
        return dataloader.get_dataloaders()
    
    def train_epoch(self) -> float:
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        for batch_idx, (images, labels) in enumerate(self.train_loader):
            images, labels = images.to(self.device), labels.to(self.device)
            
            # 前向传播
            self.optimizer.zero_grad()
            outputs = self.model(images)
            loss = self.criterion(outputs, labels)
            
            # 反向传播
            loss.backward()
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
        
        return total_loss / num_batches
    
    def validate(self) -> Dict[str, float]:
        """验证模型"""
        self.model.eval()
        self.metrics_tracker.reset()
        
        with torch.no_grad():
            for images, labels in self.val_loader:
                images, labels = images.to(self.device), labels.to(self.device)
                
                outputs = self.model(images)
                probabilities = torch.softmax(outputs, dim=1)
                predictions = torch.argmax(outputs, dim=1)
                
                self.metrics_tracker.update(labels, predictions, probabilities)
        
        return self.metrics_tracker.compute()
    
    def test(self) -> Dict[str, float]:
        """测试模型"""
        self.model.eval()
        self.metrics_tracker.reset()
        
        with torch.no_grad():
            for images, labels in self.test_loader:
                images, labels = images.to(self.device), labels.to(self.device)
                
                outputs = self.model(images)
                probabilities = torch.softmax(outputs, dim=1)
                predictions = torch.argmax(outputs, dim=1)
                
                self.metrics_tracker.update(labels, predictions, probabilities)
        
        test_metrics = self.metrics_tracker.compute()
        
        # 保存测试结果
        self.test_results = {
            key: float(value) for key, value in test_metrics.items()
        }
        
        return test_metrics
    
    def train(self):
        """完整训练流程"""
        print(f"开始训练 {self.config.model_name} 在 {self.config.dataset_name} 数据集上...")
        print(f"设备: {self.device}")
        print(f"训练轮数: {self.config.epochs}")
        
        for epoch in range(1, self.config.epochs + 1):
            # 训练
            train_loss = self.train_epoch()
            
            # 验证
            val_metrics = self.validate()
            
            # 更新学习率
            self.scheduler.step()
            current_lr = self.optimizer.param_groups[0]['lr']
            
            # 记录历史
            self.training_history['train_loss'].append(train_loss)
            self.training_history['val_accuracy'].append(val_metrics['accuracy'])
            self.training_history['val_precision'].append(val_metrics['precision'])
            self.training_history['val_recall'].append(val_metrics['recall'])
            self.training_history['val_f1'].append(val_metrics['f1'])
            self.training_history['val_auc'].append(val_metrics['auc'])
            self.training_history['learning_rate'].append(current_lr)
            self.training_history['epochs'].append(epoch)
            
            # 添加数据集特定指标
            if self.config.dataset_name.lower() == "aptos":
                self.training_history['val_qwk'].append(val_metrics['qwk'])
            
            # 获取主要指标
            primary_metric = get_primary_metric(val_metrics, self.config.dataset_name.lower())
            
            # 保存最佳模型
            if primary_metric > self.best_metric:
                self.best_metric = primary_metric
                self.training_history['best_epoch'] = epoch
                self.training_history['best_metric'] = self.best_metric
                
                if self.config.save_best_model:
                    self._save_model()
            
            # 打印进度
            primary_metric_name = self.config.primary_metric.upper()
            print(f"Epoch {epoch}/{self.config.epochs} - "
                  f"Loss: {train_loss:.4f}, "
                  f"Val Acc: {val_metrics['accuracy']:.4f}, "
                  f"Val {primary_metric_name}: {primary_metric:.4f}")
        
        print(f"训练完成! 最佳 {primary_metric_name}: {self.best_metric:.4f} (Epoch {self.training_history['best_epoch']})")
    
    def _save_model(self):
        """保存最佳模型"""
        os.makedirs(self.config.models_dir, exist_ok=True)
        model_path = os.path.join(
            self.config.models_dir, 
            f"best_{self.config.model_name.lower()}_{self.config.dataset_name.lower()}.pth"
        )
        torch.save(self.model.state_dict(), model_path)
    
    def save_training_metrics(self, filename: Optional[str] = None):
        """保存训练指标"""
        os.makedirs(self.config.results_dir, exist_ok=True)
        
        if filename is None:
            filename = f"{self.config.model_name.lower()}_{self.config.dataset_name.lower()}_metrics.json"
        
        filepath = os.path.join(self.config.results_dir, filename)
        
        # 准备保存数据
        save_data = {
            'timestamp': datetime.now().isoformat(),
            'test_results': self.test_results,
            'training_history': self.training_history,
            'training_config': self.config.to_dict()
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, indent=4, ensure_ascii=False)
        
        print(f"训练指标已保存到: {filepath}")


if __name__ == "__main__":
    print("基础训练器类定义完成!")
