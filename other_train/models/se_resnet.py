"""
SE-ResNet (Squeeze-and-Excitation ResNet) 模型实现
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision import models
from typing import Type, Union, List, Optional


class SEBlock(nn.Module):
    """Squeeze-and-Excitation Block"""
    
    def __init__(self, channels: int, reduction: int = 16):
        super(SEBlock, self).__init__()
        
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(channels, channels // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channels // reduction, channels, bias=False),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        b, c, _, _ = x.size()
        
        # Squeeze: Global Average Pooling
        y = self.avg_pool(x).view(b, c)
        
        # Excitation: FC layers
        y = self.fc(y).view(b, c, 1, 1)
        
        # Scale: 重新校准特征图
        return x * y.expand_as(x)


class SEBasicBlock(nn.Module):
    """SE-ResNet Basic Block (for ResNet18/34)"""
    expansion = 1
    
    def __init__(self, inplanes: int, planes: int, stride: int = 1,
                 downsample: Optional[nn.Module] = None, reduction: int = 16):
        super(SEBasicBlock, self).__init__()
        
        self.conv1 = nn.Conv2d(inplanes, planes, kernel_size=3, stride=stride,
                              padding=1, bias=False)
        self.bn1 = nn.BatchNorm2d(planes)
        self.relu = nn.ReLU(inplace=True)
        
        self.conv2 = nn.Conv2d(planes, planes, kernel_size=3, stride=1,
                              padding=1, bias=False)
        self.bn2 = nn.BatchNorm2d(planes)
        
        # SE Block
        self.se = SEBlock(planes, reduction)
        
        self.downsample = downsample
        self.stride = stride
    
    def forward(self, x):
        identity = x
        
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)
        
        out = self.conv2(out)
        out = self.bn2(out)
        
        # Apply SE block
        out = self.se(out)
        
        if self.downsample is not None:
            identity = self.downsample(x)
        
        out += identity
        out = self.relu(out)
        
        return out


class SEBottleneck(nn.Module):
    """SE-ResNet Bottleneck Block (for ResNet50/101/152)"""
    expansion = 4
    
    def __init__(self, inplanes: int, planes: int, stride: int = 1,
                 downsample: Optional[nn.Module] = None, reduction: int = 16):
        super(SEBottleneck, self).__init__()
        
        self.conv1 = nn.Conv2d(inplanes, planes, kernel_size=1, bias=False)
        self.bn1 = nn.BatchNorm2d(planes)
        
        self.conv2 = nn.Conv2d(planes, planes, kernel_size=3, stride=stride,
                              padding=1, bias=False)
        self.bn2 = nn.BatchNorm2d(planes)
        
        self.conv3 = nn.Conv2d(planes, planes * self.expansion, kernel_size=1, bias=False)
        self.bn3 = nn.BatchNorm2d(planes * self.expansion)
        
        # SE Block
        self.se = SEBlock(planes * self.expansion, reduction)
        
        self.relu = nn.ReLU(inplace=True)
        self.downsample = downsample
        self.stride = stride
    
    def forward(self, x):
        identity = x
        
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)
        
        out = self.conv2(out)
        out = self.bn2(out)
        out = self.relu(out)
        
        out = self.conv3(out)
        out = self.bn3(out)
        
        # Apply SE block
        out = self.se(out)
        
        if self.downsample is not None:
            identity = self.downsample(x)
        
        out += identity
        out = self.relu(out)
        
        return out


class SEResNet(nn.Module):
    """SE-ResNet模型"""
    
    def __init__(self, block: Type[Union[SEBasicBlock, SEBottleneck]], 
                 layers: List[int], num_classes: int = 1000, 
                 reduction: int = 16, pretrained: bool = True):
        super(SEResNet, self).__init__()
        
        if pretrained:
            # 使用预训练的ResNet并添加SE模块
            if block == SEBottleneck and layers == [3, 4, 6, 3]:
                # SE-ResNet50
                self.backbone = models.resnet50(pretrained=True)
                self._add_se_modules(reduction)
            elif block == SEBottleneck and layers == [3, 4, 23, 3]:
                # SE-ResNet101
                self.backbone = models.resnet101(pretrained=True)
                self._add_se_modules(reduction)
            else:
                print("警告: 不支持的预训练SE-ResNet配置，将从头训练")
                pretrained = False
        
        if not pretrained:
            # 从头构建SE-ResNet
            self.inplanes = 64
            
            # Stem layers
            self.conv1 = nn.Conv2d(3, 64, kernel_size=7, stride=2, padding=3, bias=False)
            self.bn1 = nn.BatchNorm2d(64)
            self.relu = nn.ReLU(inplace=True)
            self.maxpool = nn.MaxPool2d(kernel_size=3, stride=2, padding=1)
            
            # Residual layers
            self.layer1 = self._make_layer(block, 64, layers[0], reduction=reduction)
            self.layer2 = self._make_layer(block, 128, layers[1], stride=2, reduction=reduction)
            self.layer3 = self._make_layer(block, 256, layers[2], stride=2, reduction=reduction)
            self.layer4 = self._make_layer(block, 512, layers[3], stride=2, reduction=reduction)
            
            # Classification head
            self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
            self.fc = nn.Linear(512 * block.expansion, num_classes)
            
            # 初始化权重
            self._initialize_weights()
        else:
            # 替换分类器
            if hasattr(self.backbone, 'fc'):
                num_features = self.backbone.fc.in_features
                self.backbone.fc = nn.Linear(num_features, num_classes)
    
    def _add_se_modules(self, reduction: int):
        """为预训练ResNet添加SE模块"""
        def add_se_to_layer(layer):
            for block in layer:
                if hasattr(block, 'conv3'):  # Bottleneck block
                    channels = block.conv3.out_channels
                else:  # Basic block
                    channels = block.conv2.out_channels
                
                # 在每个残差块后添加SE模块
                se_block = SEBlock(channels, reduction)
                
                # 修改forward方法以包含SE
                original_forward = block.forward
                def new_forward(x, se=se_block, orig_forward=original_forward):
                    out = orig_forward(x)
                    # 在残差连接之前应用SE
                    # 需要重新实现以正确插入SE模块
                    return out
                
                # 这里简化处理，实际应用中可能需要更复杂的修改
                block.se = se_block
        
        # 为每一层添加SE模块
        add_se_to_layer(self.backbone.layer1)
        add_se_to_layer(self.backbone.layer2)
        add_se_to_layer(self.backbone.layer3)
        add_se_to_layer(self.backbone.layer4)
    
    def _make_layer(self, block: Type[Union[SEBasicBlock, SEBottleneck]], 
                   planes: int, blocks: int, stride: int = 1, reduction: int = 16):
        """构建残差层"""
        downsample = None
        if stride != 1 or self.inplanes != planes * block.expansion:
            downsample = nn.Sequential(
                nn.Conv2d(self.inplanes, planes * block.expansion,
                         kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm2d(planes * block.expansion),
            )
        
        layers = []
        layers.append(block(self.inplanes, planes, stride, downsample, reduction))
        self.inplanes = planes * block.expansion
        
        for _ in range(1, blocks):
            layers.append(block(self.inplanes, planes, reduction=reduction))
        
        return nn.Sequential(*layers)
    
    def _initialize_weights(self):
        """初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        if hasattr(self, 'backbone'):
            # 使用预训练backbone
            return self.backbone(x)
        else:
            # 从头构建的模型
            x = self.conv1(x)
            x = self.bn1(x)
            x = self.relu(x)
            x = self.maxpool(x)
            
            x = self.layer1(x)
            x = self.layer2(x)
            x = self.layer3(x)
            x = self.layer4(x)
            
            x = self.avgpool(x)
            x = torch.flatten(x, 1)
            x = self.fc(x)
            
            return x


def create_se_resnet(num_classes: int = 1000, pretrained: bool = True,
                    layers: List[int] = [3, 4, 6, 3], reduction: int = 16) -> SEResNet:
    """
    创建SE-ResNet模型
    
    Args:
        num_classes: 分类数量
        pretrained: 是否使用预训练权重
        layers: 每层的块数 [3,4,6,3] for ResNet50
        reduction: SE模块的缩减比例
    
    Returns:
        SE-ResNet模型
    """
    if layers == [2, 2, 2, 2]:
        block = SEBasicBlock  # SE-ResNet18
    elif layers == [3, 4, 6, 3]:
        block = SEBottleneck  # SE-ResNet50
    elif layers == [3, 4, 23, 3]:
        block = SEBottleneck  # SE-ResNet101
    else:
        block = SEBottleneck  # 默认使用Bottleneck
    
    return SEResNet(
        block=block,
        layers=layers,
        num_classes=num_classes,
        reduction=reduction,
        pretrained=pretrained
    )


if __name__ == "__main__":
    # 测试SE-ResNet模型
    print("测试SE-ResNet模型...")
    
    # 创建模型
    model = create_se_resnet(num_classes=5, pretrained=False)
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 测试前向传播
    x = torch.randn(2, 3, 224, 224)
    with torch.no_grad():
        output = model(x)
    print(f"输入形状: {x.shape}")
    print(f"输出形状: {output.shape}")
    
    print("SE-ResNet模型测试完成!")
