#!/usr/bin/env python3
"""
批量运行所有基线模型训练脚本
"""

import subprocess
import sys
import os
from datetime import datetime

def run_script(script_name):
    """运行单个训练脚本"""
    print(f"\n{'='*60}")
    print(f"开始运行: {script_name}")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}")
    
    try:
        # 使用指定的Python解释器运行脚本
        python_exe = "D:\\anaconda3\\envs\\MC\\python.exe"
        result = subprocess.run([python_exe, script_name], 
                              capture_output=False, 
                              text=True, 
                              check=True)
        print(f"\n✅ {script_name} 训练完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n❌ {script_name} 训练失败: {e}")
        return False
    except Exception as e:
        print(f"\n❌ {script_name} 运行出错: {e}")
        return False

def main():
    """主函数"""
    print("开始批量运行基线模型训练")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 所有训练脚本列表
    scripts = [
        # DenseNet
        "train_densenet_APTOS.py",
        "train_densenet_ODIR.py",
        
        # EfficientNet
        "train_efficientnet_APTOS.py", 
        "train_efficientnet_ODIR.py",
        
        # Vision Transformer
        "train_vit_APTOS.py",
        "train_vit_ODIR.py",
        
        # SE-ResNet
        "train_seresnet_APTOS.py",
        "train_seresnet_ODIR.py",
        
        # MSNets
        "train_msnets_APTOS.py",
        "train_msnets_ODIR.py"
    ]
    
    # 检查脚本是否存在
    missing_scripts = []
    for script in scripts:
        if not os.path.exists(script):
            missing_scripts.append(script)
    
    if missing_scripts:
        print("❌ 以下脚本文件不存在:")
        for script in missing_scripts:
            print(f"   - {script}")
        return
    
    # 运行统计
    total_scripts = len(scripts)
    successful_runs = 0
    failed_runs = 0
    
    # 逐个运行脚本
    for i, script in enumerate(scripts, 1):
        print(f"\n进度: {i}/{total_scripts}")
        
        if run_script(script):
            successful_runs += 1
        else:
            failed_runs += 1
            
            # 询问是否继续
            response = input(f"\n{script} 运行失败，是否继续运行其他脚本？(y/n): ")
            if response.lower() != 'y':
                print("用户选择停止运行")
                break
    
    # 输出总结
    print(f"\n{'='*60}")
    print("批量训练完成总结")
    print(f"{'='*60}")
    print(f"总脚本数: {total_scripts}")
    print(f"成功运行: {successful_runs}")
    print(f"失败运行: {failed_runs}")
    print(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if successful_runs == total_scripts:
        print("🎉 所有基线模型训练完成！")
    else:
        print(f"⚠️  有 {failed_runs} 个脚本运行失败，请检查错误信息")

if __name__ == "__main__":
    # 确保在正确的目录中运行
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    main()
