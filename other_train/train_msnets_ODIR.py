import torch
import torch.nn as nn
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR
import torchvision.models as models
import torch.nn.functional as F
import numpy as np
from sklearn.metrics import accuracy_score, roc_auc_score
from sklearn.metrics import precision_recall_fscore_support
from tqdm import tqdm
import json
import os
from datetime import datetime
import sys
sys.path.append('..')

from dataloader_ODIR import ODIRDataLoader

# 获取当前脚本文件名（不含扩展名）
SCRIPT_NAME = os.path.splitext(os.path.basename(__file__))[0]


class MultiScaleBlock(nn.Module):
    """多尺度特征提取块"""
    def __init__(self, in_channels, out_channels):
        super(MultiScaleBlock, self).__init__()
        # 不同尺度的卷积分支
        self.branch1 = nn.Sequential(
            nn.Conv2d(in_channels, out_channels//4, kernel_size=1),
            nn.BatchNorm2d(out_channels//4),
            nn.ReLU(inplace=True)
        )
        
        self.branch2 = nn.Sequential(
            nn.Conv2d(in_channels, out_channels//4, kernel_size=1),
            nn.BatchNorm2d(out_channels//4),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels//4, out_channels//4, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels//4),
            nn.ReLU(inplace=True)
        )
        
        self.branch3 = nn.Sequential(
            nn.Conv2d(in_channels, out_channels//4, kernel_size=1),
            nn.BatchNorm2d(out_channels//4),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels//4, out_channels//4, kernel_size=5, padding=2),
            nn.BatchNorm2d(out_channels//4),
            nn.ReLU(inplace=True)
        )
        
        self.branch4 = nn.Sequential(
            nn.MaxPool2d(kernel_size=3, stride=1, padding=1),
            nn.Conv2d(in_channels, out_channels//4, kernel_size=1),
            nn.BatchNorm2d(out_channels//4),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, x):
        branch1 = self.branch1(x)
        branch2 = self.branch2(x)
        branch3 = self.branch3(x)
        branch4 = self.branch4(x)
        
        return torch.cat([branch1, branch2, branch3, branch4], dim=1)


class MSNetsModel(nn.Module):
    """多尺度网络模型"""
    def __init__(self, num_classes=2, pretrained=True):
        super(MSNetsModel, self).__init__()
        # 使用ResNet50作为backbone
        resnet = models.resnet50(pretrained=pretrained)
        
        # 提取特征提取部分
        self.backbone = nn.Sequential(*list(resnet.children())[:-2])  # 去掉avgpool和fc
        
        # 多尺度特征提取模块
        self.ms_block1 = MultiScaleBlock(2048, 512)
        self.ms_block2 = MultiScaleBlock(512, 256)
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool2d(1)
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(256, 128),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(128, num_classes)
        )
        
    def forward(self, x):
        # 特征提取
        features = self.backbone(x)
        
        # 多尺度特征处理
        ms_features1 = self.ms_block1(features)
        ms_features2 = self.ms_block2(ms_features1)
        
        # 全局池化
        pooled = self.global_pool(ms_features2)
        pooled = pooled.view(pooled.size(0), -1)
        
        # 分类
        output = self.classifier(pooled)
        
        return output


class DRTrainer:
    def __init__(self, num_classes=2, batch_size=32, lr=1e-4, epochs=30):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'mps' if torch.backends.mps.is_available() else 'cpu')
        self.num_classes = num_classes
        self.epochs = epochs

        # 数据加载
        self.dataloader = ODIRDataLoader(batch_size=batch_size)
        self.train_loader, self.val_loader, self.test_loader = self.dataloader.get_dataloaders()

        # 模型
        self.model = MSNetsModel(num_classes=num_classes).to(self.device)

        # 冻结backbone，只训练多尺度模块和分类器
        self._freeze_backbone()

        # 优化器和调度器
        self.optimizer = optim.Adam(self.model.parameters(), lr=lr)
        self.scheduler = CosineAnnealingLR(self.optimizer, T_max=epochs)
        self.criterion = nn.CrossEntropyLoss()

        self.best_auc = 0.0

        # 训练历史记录
        self.training_history = {
            'train_loss': [],
            'val_accuracy': [],
            'val_auc': [],
            'val_precision': [],
            'val_recall': [],
            'val_f1': [],
            'learning_rate': [],
            'epochs': [],
            'best_epoch': 0,
            'best_auc': 0.0
        }

        # 测试结果记录
        self.test_results = None

    def _freeze_backbone(self):
        """冻结backbone，只训练多尺度模块和分类器"""
        # 冻结backbone
        for param in self.model.backbone.parameters():
            param.requires_grad = False
        
        # 解冻多尺度模块和分类器
        for param in self.model.ms_block1.parameters():
            param.requires_grad = True
        for param in self.model.ms_block2.parameters():
            param.requires_grad = True
        for param in self.model.classifier.parameters():
            param.requires_grad = True

    def train_epoch(self):
        self.model.train()
        total_loss = 0

        for images, labels in tqdm(self.train_loader, desc="Training"):
            images, labels = images.to(self.device), labels.to(self.device)

            self.optimizer.zero_grad()
            outputs = self.model(images)
            loss = self.criterion(outputs, labels)
            loss.backward()
            self.optimizer.step()

            total_loss += loss.item()

        return total_loss / len(self.train_loader)

    def evaluate(self, loader):
        self.model.eval()
        all_preds, all_labels, all_probs = [], [], []

        with torch.no_grad():
            for images, labels in loader:
                images, labels = images.to(self.device), labels.to(self.device)
                outputs = self.model(images)
                probs = torch.softmax(outputs, dim=1)
                preds = torch.argmax(outputs, dim=1)

                all_preds.extend(preds.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                all_probs.extend(probs.cpu().numpy())

        return np.array(all_preds), np.array(all_labels), np.array(all_probs)

    def calculate_metrics(self, preds, labels, probs):
        """计算评估指标"""
        # 基础指标
        acc = accuracy_score(labels, preds)

        # AUC - 对于二分类，使用正类的概率
        if self.num_classes == 2:
            auc = roc_auc_score(labels, probs[:, 1])  # 使用正类(类别1)的概率
        else:
            auc = roc_auc_score(labels, probs, multi_class='ovr', average='macro')

        # 整体precision, recall, f1 (weighted average)
        precision, recall, f1, _ = precision_recall_fscore_support(
            labels, preds, average='weighted', zero_division=0
        )

        return {
            'accuracy': acc,
            'auc': auc,
            'precision': precision,
            'recall': recall,
            'f1': f1
        }

    def save_training_metrics(self, filename=None):
        """保存训练指标到JSON文件"""
        # 确保results文件夹存在
        os.makedirs('../results', exist_ok=True)

        filename = f'{SCRIPT_NAME}_metrics.json'

        filepath = os.path.join('../results', filename)

        # 添加训练配置信息
        training_config = {
            'model': 'MSNets (Multi-Scale Networks)',
            'num_classes': self.num_classes,
            'epochs': self.epochs,
            'batch_size': self.dataloader.batch_size,
            'device': str(self.device),
            'optimizer': 'Adam',
            'scheduler': 'CosineAnnealingLR',
            'criterion': 'CrossEntropyLoss'
        }

        # 准备保存的数据，测试结果放在最前面
        save_data = {
            'timestamp': datetime.now().isoformat()
        }

        # 如果有测试结果，放在最前面
        if self.test_results is not None:
            save_data['test_results'] = self.test_results

        # 然后是训练历史
        save_data['training_history'] = self.training_history

        # 最后是训练配置
        save_data['training_config'] = training_config

        # 保存到JSON文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, indent=4, ensure_ascii=False)

        print(f"训练指标已保存到: {filepath}")
        return filepath

    def train(self):
        print(f"开始训练MSNets，设备: {self.device}")

        for epoch in range(self.epochs):
            # 训练
            train_loss = self.train_epoch()

            # 验证
            val_preds, val_labels, val_probs = self.evaluate(self.val_loader)
            val_metrics = self.calculate_metrics(val_preds, val_labels, val_probs)

            # 获取当前学习率
            current_lr = self.optimizer.param_groups[0]['lr']

            # 记录训练指标
            self.training_history['train_loss'].append(train_loss)
            self.training_history['val_accuracy'].append(val_metrics['accuracy'])
            self.training_history['val_auc'].append(val_metrics['auc'])
            self.training_history['val_precision'].append(val_metrics['precision'])
            self.training_history['val_recall'].append(val_metrics['recall'])
            self.training_history['val_f1'].append(val_metrics['f1'])
            self.training_history['learning_rate'].append(current_lr)
            self.training_history['epochs'].append(epoch + 1)

            # 更新学习率
            self.scheduler.step()

            # 保存最佳模型（基于AUC）
            if val_metrics['auc'] > self.best_auc:
                self.best_auc = val_metrics['auc']
                self.training_history['best_auc'] = self.best_auc
                self.training_history['best_epoch'] = epoch + 1
                # 确保models_pth文件夹存在
                os.makedirs('../models_pth', exist_ok=True)
                model_filename = f'best_{SCRIPT_NAME}.pth'
                torch.save(self.model.state_dict(), f'../models_pth/{model_filename}')

            # 打印结果
            print(f"Epoch {epoch + 1}/{self.epochs}")
            print(f"Train Loss: {train_loss:.4f}")
            print(f"Val - Acc: {val_metrics['accuracy']:.4f}, AUC: {val_metrics['auc']:.4f}, "
                  f"F1: {val_metrics['f1']:.4f}, Precision: {val_metrics['precision']:.4f}")
            print(f"Learning Rate: {current_lr:.6f}")
            print("-" * 60)

    def test(self):
        """测试集评估"""
        print("加载最佳模型进行测试...")
        model_filename = f'best_{SCRIPT_NAME}.pth'
        self.model.load_state_dict(torch.load(f'../models_pth/{model_filename}'))

        test_preds, test_labels, test_probs = self.evaluate(self.test_loader)
        test_metrics = self.calculate_metrics(test_preds, test_labels, test_probs)

        print("=== 测试集结果 ===")
        print(f"Accuracy: {test_metrics['accuracy']:.4f}")
        print(f"AUC: {test_metrics['auc']:.4f}")
        print(f"Precision: {test_metrics['precision']:.4f}")
        print(f"Recall: {test_metrics['recall']:.4f}")
        print(f"F1: {test_metrics['f1']:.4f}")

        # 保存测试结果到独立的test_results中
        self.test_results = {
            'accuracy': float(test_metrics['accuracy']),
            'auc': float(test_metrics['auc']),
            'precision': float(test_metrics['precision']),
            'recall': float(test_metrics['recall']),
            'f1': float(test_metrics['f1'])
        }

        self.save_training_metrics()

        return test_metrics


if __name__ == "__main__":
    # 训练
    trainer = DRTrainer(num_classes=2, batch_size=32, lr=1e-4, epochs=15)
    trainer.train()

    # 测试
    trainer.test()
