"""
实验执行器 - 批量运行对比实验
"""
import os
import json
import time
import traceback
from datetime import datetime
from typing import List, Dict, Optional
import pandas as pd

import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from other_train.config import get_config
from other_train.trainers import get_train_function


class ExperimentRunner:
    """实验执行器类"""
    
    def __init__(self, results_dir: str = "results", models_dir: str = "models_pth"):
        self.results_dir = results_dir
        self.models_dir = models_dir
        self.experiment_results = {}
        
        # 确保目录存在
        os.makedirs(results_dir, exist_ok=True)
        os.makedirs(models_dir, exist_ok=True)
    
    def run_single_experiment(self, model_name: str, dataset: str) -> Dict:
        """
        运行单个实验
        
        Args:
            model_name: 模型名称
            dataset: 数据集名称
        
        Returns:
            实验结果字典
        """
        print(f"\n{'='*80}")
        print(f"开始实验: {model_name.upper()} on {dataset.upper()}")
        print(f"{'='*80}")
        
        start_time = time.time()
        
        try:
            # 获取训练函数
            train_function = get_train_function(model_name, dataset)
            
            # 执行训练
            trainer = train_function()
            
            # 获取测试结果
            test_results = trainer.test_results
            training_config = trainer.config.to_dict()
            
            end_time = time.time()
            training_time = end_time - start_time
            
            # 构建实验结果
            experiment_result = {
                'model_name': model_name,
                'dataset': dataset,
                'status': 'success',
                'training_time_seconds': training_time,
                'test_results': test_results,
                'training_config': training_config,
                'timestamp': datetime.now().isoformat(),
                'error_message': None
            }
            
            print(f"\n✅ 实验成功完成!")
            print(f"训练时间: {training_time:.2f} 秒")
            if test_results:
                primary_metric = training_config.get('primary_metric', 'accuracy')
                if primary_metric in test_results:
                    print(f"主要指标 ({primary_metric.upper()}): {test_results[primary_metric]:.4f}")
            
        except Exception as e:
            end_time = time.time()
            training_time = end_time - start_time
            
            error_message = f"{str(e)}\n{traceback.format_exc()}"
            
            experiment_result = {
                'model_name': model_name,
                'dataset': dataset,
                'status': 'failed',
                'training_time_seconds': training_time,
                'test_results': None,
                'training_config': None,
                'timestamp': datetime.now().isoformat(),
                'error_message': error_message
            }
            
            print(f"\n❌ 实验失败!")
            print(f"错误信息: {str(e)}")
        
        return experiment_result
    
    def run_all_experiments(self, models: List[str], datasets: List[str]) -> Dict:
        """
        运行所有实验组合
        
        Args:
            models: 模型列表
            datasets: 数据集列表
        
        Returns:
            所有实验结果
        """
        print(f"\n🚀 开始批量实验")
        print(f"模型: {models}")
        print(f"数据集: {datasets}")
        print(f"总实验数: {len(models) * len(datasets)}")
        
        all_results = {}
        total_experiments = len(models) * len(datasets)
        completed_experiments = 0
        
        for model_name in models:
            for dataset in datasets:
                experiment_key = f"{model_name}_{dataset}"
                
                print(f"\n进度: {completed_experiments + 1}/{total_experiments}")
                
                result = self.run_single_experiment(model_name, dataset)
                all_results[experiment_key] = result
                
                completed_experiments += 1
                
                # 保存中间结果
                self._save_intermediate_results(all_results)
        
        # 保存最终结果
        self._save_final_results(all_results)
        
        # 生成结果分析
        self._generate_analysis(all_results)
        
        print(f"\n🎉 所有实验完成!")
        print(f"成功: {sum(1 for r in all_results.values() if r['status'] == 'success')}")
        print(f"失败: {sum(1 for r in all_results.values() if r['status'] == 'failed')}")
        
        return all_results
    
    def _save_intermediate_results(self, results: Dict):
        """保存中间结果"""
        filepath = os.path.join(self.results_dir, "experiment_results_intermediate.json")
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=4, ensure_ascii=False)
    
    def _save_final_results(self, results: Dict):
        """保存最终结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filepath = os.path.join(self.results_dir, f"experiment_results_{timestamp}.json")
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=4, ensure_ascii=False)
        
        print(f"\n📊 实验结果已保存到: {filepath}")
    
    def _generate_analysis(self, results: Dict):
        """生成结果分析"""
        # 创建结果表格
        analysis_data = []
        
        for experiment_key, result in results.items():
            if result['status'] == 'success' and result['test_results']:
                row = {
                    'Model': result['model_name'],
                    'Dataset': result['dataset'],
                    'Status': result['status'],
                    'Training_Time(s)': result['training_time_seconds']
                }
                
                # 添加测试指标
                test_results = result['test_results']
                for metric, value in test_results.items():
                    row[metric.upper()] = value
                
                analysis_data.append(row)
            else:
                # 失败的实验
                row = {
                    'Model': result['model_name'],
                    'Dataset': result['dataset'],
                    'Status': result['status'],
                    'Training_Time(s)': result['training_time_seconds'],
                    'Error': result['error_message'][:100] if result['error_message'] else 'Unknown'
                }
                analysis_data.append(row)
        
        # 保存为CSV
        if analysis_data:
            df = pd.DataFrame(analysis_data)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            csv_filepath = os.path.join(self.results_dir, f"experiment_analysis_{timestamp}.csv")
            df.to_csv(csv_filepath, index=False)
            print(f"📈 分析结果已保存到: {csv_filepath}")
            
            # 打印简要分析
            print(f"\n📋 实验结果摘要:")
            print(df.to_string(index=False))
    
    def compare_models(self, results_file: str, dataset: str, primary_metric: str):
        """
        比较不同模型在指定数据集上的性能
        
        Args:
            results_file: 结果文件路径
            dataset: 数据集名称
            primary_metric: 主要比较指标
        """
        with open(results_file, 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        # 筛选指定数据集的结果
        dataset_results = {}
        for key, result in results.items():
            if result['dataset'] == dataset and result['status'] == 'success':
                model_name = result['model_name']
                if result['test_results'] and primary_metric in result['test_results']:
                    dataset_results[model_name] = result['test_results'][primary_metric]
        
        # 排序并显示
        sorted_results = sorted(dataset_results.items(), key=lambda x: x[1], reverse=True)
        
        print(f"\n🏆 {dataset.upper()}数据集上的模型性能排名 (按{primary_metric.upper()}排序):")
        print("-" * 50)
        for rank, (model, score) in enumerate(sorted_results, 1):
            print(f"{rank}. {model.upper()}: {score:.4f}")


def run_comparison_experiments():
    """运行完整的对比实验"""
    # 定义实验配置
    models = ['densenet', 'efficientnet', 'vit', 'se_resnet', 'msnets']
    datasets = ['aptos', 'odir']
    
    # 创建实验执行器
    runner = ExperimentRunner()
    
    # 运行所有实验
    results = runner.run_all_experiments(models, datasets)
    
    return results


if __name__ == "__main__":
    # 运行对比实验
    results = run_comparison_experiments()
    
    print("\n实验执行完成!")
