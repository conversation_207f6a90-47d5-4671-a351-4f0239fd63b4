# 基线模型训练脚本

本文件夹包含了用于对比验证的多个基线模型训练脚本，支持APTOS和ODIR两个数据集。

## 模型列表

### 1. DenseNet
- **APTOS数据集**: `train_densenet_APTOS.py`
- **ODIR数据集**: `train_densenet_ODIR.py`
- **模型**: DenseNet121 (torchvision预训练)
- **评估指标**: APTOS使用QWK，ODIR使用AUC

### 2. EfficientNet
- **APTOS数据集**: `train_efficientnet_APTOS.py`
- **ODIR数据集**: `train_efficientnet_ODIR.py`
- **模型**: EfficientNet-B0 (timm预训练)
- **评估指标**: APTOS使用QWK，ODIR使用AUC

### 3. Vision Transformer (ViT)
- **APTOS数据集**: `train_vit_APTOS.py`
- **ODIR数据集**: `train_vit_ODIR.py`
- **模型**: ViT Base Patch16 224 (timm预训练)
- **评估指标**: APTOS使用QWK，ODIR使用AUC

### 4. SE-ResNet
- **APTOS数据集**: `train_seresnet_APTOS.py`
- **ODIR数据集**: `train_seresnet_ODIR.py`
- **模型**: SE-ResNet50 (timm预训练)
- **评估指标**: APTOS使用QWK，ODIR使用AUC

### 5. MSNets (Multi-Scale Networks)
- **APTOS数据集**: `train_msnets_APTOS.py`
- **ODIR数据集**: `train_msnets_ODIR.py`
- **模型**: 自定义多尺度网络（基于ResNet50 backbone）
- **评估指标**: APTOS使用QWK，ODIR使用AUC

## 训练配置

所有模型使用相同的训练参数以确保公平对比：

- **批次大小**: 32
- **学习率**: 1e-4
- **优化器**: Adam
- **学习率调度**: CosineAnnealingLR
- **训练轮数**: 15
- **损失函数**: CrossEntropyLoss

## 数据集配置

### APTOS数据集
- **类别数**: 5 (糖尿病视网膜病变严重程度: 0-4)
- **主要评估指标**: Quadratic Weighted Kappa (QWK)
- **其他指标**: Accuracy, AUC, Precision, Recall, F1

### ODIR数据集
- **类别数**: 2 (正常/异常)
- **主要评估指标**: AUC (Area Under Curve)
- **其他指标**: Accuracy, Precision, Recall, F1

## 使用方法

### 1. 环境要求
```bash
pip install torch torchvision timm scikit-learn tqdm
```

### 2. 运行训练脚本
```bash
# 进入other_train目录
cd other_train

# 训练DenseNet在APTOS数据集上
python train_densenet_APTOS.py

# 训练EfficientNet在ODIR数据集上
python train_efficientnet_ODIR.py

# 其他模型类似...
```

### 3. 输出文件

每个训练脚本会生成以下文件：

- **模型权重**: `../models_pth/best_[脚本名].pth`
- **训练指标**: `../results/[脚本名]_metrics.json`

## 文件结构

```
other_train/
├── README.md                    # 本说明文件
├── train_densenet_APTOS.py     # DenseNet APTOS训练
├── train_densenet_ODIR.py      # DenseNet ODIR训练
├── train_efficientnet_APTOS.py # EfficientNet APTOS训练
├── train_efficientnet_ODIR.py  # EfficientNet ODIR训练
├── train_vit_APTOS.py          # ViT APTOS训练
├── train_vit_ODIR.py           # ViT ODIR训练
├── train_seresnet_APTOS.py     # SE-ResNet APTOS训练
├── train_seresnet_ODIR.py      # SE-ResNet ODIR训练
├── train_msnets_APTOS.py       # MSNets APTOS训练
└── train_msnets_ODIR.py        # MSNets ODIR训练
```

## 结果分析

训练完成后，可以通过以下方式分析结果：

1. **查看训练指标**: 检查 `../results/` 文件夹中的JSON文件
2. **模型对比**: 比较不同模型在相同数据集上的性能
3. **数据集对比**: 比较同一模型在不同数据集上的表现

## 注意事项

1. **冻结策略**: 所有模型都采用冻结backbone、只训练分类层的策略，以加快训练速度
2. **评估指标**: APTOS数据集主要关注QWK指标，ODIR数据集主要关注AUC指标
3. **模型保存**: 基于验证集上的最佳指标保存模型
4. **路径设置**: 脚本假设从other_train目录运行，相对路径指向上级目录

## 扩展说明

如需修改训练参数或添加新的基线模型，可以参考现有脚本的结构进行扩展。所有脚本都遵循相同的代码结构和命名规范，便于维护和对比。
