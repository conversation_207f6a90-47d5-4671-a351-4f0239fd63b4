"""
DenseNet训练器
"""
import os
import sys
import torch.nn as nn

# 添加路径以导入必要模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from other_train.trainers.base_trainer import BaseTrainer
from other_train.models.densenet import create_densenet
from other_train.config import get_config


class DenseNetTrainer(BaseTrainer):
    """DenseNet训练器"""
    
    def __init__(self, config):
        super().__init__(config)
    
    def _create_model(self) -> nn.Module:
        """创建DenseNet模型"""
        model_config = {
            'num_classes': self.config.num_classes,
            'pretrained': getattr(self.config, 'pretrained', True),
            'growth_rate': getattr(self.config, 'growth_rate', 32),
            'block_config': getattr(self.config, 'block_config', (6, 12, 24, 16)),
            'num_init_features': getattr(self.config, 'num_init_features', 64),
            'bn_size': getattr(self.config, 'bn_size', 4),
            'drop_rate': getattr(self.config, 'drop_rate', 0.0)
        }
        
        return create_densenet(**model_config)
    
    def _freeze_backbone(self):
        """冻结DenseNet的backbone"""
        if hasattr(self.model, 'features'):
            # 冻结特征提取层
            for param in self.model.features.parameters():
                param.requires_grad = False
            
            # 只训练分类器
            for param in self.model.classifier.parameters():
                param.requires_grad = True
            
            print("已冻结DenseNet backbone，只训练分类器")
        else:
            print("警告: 无法找到features层，未执行冻结操作")


def train_densenet_aptos():
    """训练DenseNet在APTOS数据集上"""
    print("=" * 60)
    print("开始训练 DenseNet 在 APTOS 数据集上")
    print("=" * 60)
    
    # 获取配置
    config = get_config('aptos', 'densenet')
    
    # 创建训练器
    trainer = DenseNetTrainer(config)
    
    # 训练
    trainer.train()
    
    # 测试
    test_metrics = trainer.test()
    print(f"\n测试结果:")
    for metric, value in test_metrics.items():
        print(f"{metric.upper()}: {value:.4f}")
    
    # 保存结果
    trainer.save_training_metrics()
    
    return trainer


def train_densenet_odir():
    """训练DenseNet在ODIR数据集上"""
    print("=" * 60)
    print("开始训练 DenseNet 在 ODIR 数据集上")
    print("=" * 60)
    
    # 获取配置
    config = get_config('odir', 'densenet')
    
    # 创建训练器
    trainer = DenseNetTrainer(config)
    
    # 训练
    trainer.train()
    
    # 测试
    test_metrics = trainer.test()
    print(f"\n测试结果:")
    for metric, value in test_metrics.items():
        print(f"{metric.upper()}: {value:.4f}")
    
    # 保存结果
    trainer.save_training_metrics()
    
    return trainer


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='训练DenseNet模型')
    parser.add_argument('--dataset', type=str, choices=['aptos', 'odir'], 
                       default='aptos', help='数据集选择')
    
    args = parser.parse_args()
    
    if args.dataset == 'aptos':
        trainer = train_densenet_aptos()
    elif args.dataset == 'odir':
        trainer = train_densenet_odir()
    
    print(f"\nDenseNet在{args.dataset.upper()}数据集上的训练完成!")
