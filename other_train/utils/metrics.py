"""
统一的评估指标计算工具
确保所有模型使用相同的指标计算方法
"""
import numpy as np
import torch
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_auc_score, cohen_kappa_score, confusion_matrix
)
from typing import Dict, List, Tuple, Union


def quadratic_weighted_kappa(y_true: np.ndarray, y_pred: np.ndarray, num_classes: int = 5) -> float:
    """
    计算Quadratic Weighted Kappa (QWK) - APTOS数据集的主要评估指标
    
    Args:
        y_true: 真实标签
        y_pred: 预测标签
        num_classes: 类别数量
    
    Returns:
        QWK分数
    """
    # 创建权重矩阵
    weights = np.zeros((num_classes, num_classes))
    for i in range(num_classes):
        for j in range(num_classes):
            weights[i, j] = (i - j) ** 2 / (num_classes - 1) ** 2
    
    # 计算混淆矩阵
    cm = confusion_matrix(y_true, y_pred, labels=list(range(num_classes)))
    cm = cm.astype(np.float32)
    
    # 计算期望矩阵
    hist_true = np.bincount(y_true, minlength=num_classes)
    hist_pred = np.bincount(y_pred, minlength=num_classes)
    expected = np.outer(hist_true, hist_pred).astype(np.float32)
    expected = expected / expected.sum()
    
    # 归一化混淆矩阵
    cm = cm / cm.sum()
    
    # 计算QWK
    numerator = (weights * cm).sum()
    denominator = (weights * expected).sum()
    
    if denominator == 0:
        return 0.0
    
    return 1.0 - numerator / denominator


def calculate_metrics(y_true: Union[np.ndarray, torch.Tensor], 
                     y_pred: Union[np.ndarray, torch.Tensor],
                     y_prob: Union[np.ndarray, torch.Tensor] = None,
                     num_classes: int = 5,
                     dataset_type: str = "aptos") -> Dict[str, float]:
    """
    计算所有评估指标
    
    Args:
        y_true: 真实标签
        y_pred: 预测标签
        y_prob: 预测概率 (用于AUC计算)
        num_classes: 类别数量
        dataset_type: 数据集类型 ("aptos" 或 "odir")
    
    Returns:
        包含所有指标的字典
    """
    # 转换为numpy数组
    if isinstance(y_true, torch.Tensor):
        y_true = y_true.cpu().numpy()
    if isinstance(y_pred, torch.Tensor):
        y_pred = y_pred.cpu().numpy()
    if y_prob is not None and isinstance(y_prob, torch.Tensor):
        y_prob = y_prob.cpu().numpy()
    
    # 基础指标
    metrics = {
        'accuracy': float(accuracy_score(y_true, y_pred)),
        'precision': float(precision_score(y_true, y_pred, average='weighted', zero_division=0)),
        'recall': float(recall_score(y_true, y_pred, average='weighted', zero_division=0)),
        'f1': float(f1_score(y_true, y_pred, average='weighted', zero_division=0))
    }
    
    # AUC计算
    if y_prob is not None:
        try:
            if num_classes == 2:
                # 二分类AUC
                metrics['auc'] = float(roc_auc_score(y_true, y_prob[:, 1]))
            else:
                # 多分类AUC (one-vs-rest)
                metrics['auc'] = float(roc_auc_score(y_true, y_prob, multi_class='ovr', average='weighted'))
        except ValueError:
            # 如果计算AUC失败，设为0
            metrics['auc'] = 0.0
    else:
        metrics['auc'] = 0.0
    
    # 数据集特定的主要指标
    if dataset_type.lower() == "aptos":
        # APTOS使用QWK作为主要指标
        metrics['qwk'] = float(quadratic_weighted_kappa(y_true, y_pred, num_classes))
    elif dataset_type.lower() == "odir":
        # ODIR使用AUC作为主要指标（已在上面计算）
        pass
    
    return metrics


def get_primary_metric(metrics: Dict[str, float], dataset_type: str) -> float:
    """
    获取数据集的主要评估指标
    
    Args:
        metrics: 指标字典
        dataset_type: 数据集类型
    
    Returns:
        主要指标值
    """
    if dataset_type.lower() == "aptos":
        return metrics.get('qwk', 0.0)
    elif dataset_type.lower() == "odir":
        return metrics.get('auc', 0.0)
    else:
        return metrics.get('accuracy', 0.0)


class MetricsTracker:
    """指标跟踪器类"""
    
    def __init__(self, dataset_type: str = "aptos", num_classes: int = 5):
        self.dataset_type = dataset_type
        self.num_classes = num_classes
        self.reset()
    
    def reset(self):
        """重置指标"""
        self.all_true = []
        self.all_pred = []
        self.all_prob = []
    
    def update(self, y_true: torch.Tensor, y_pred: torch.Tensor, y_prob: torch.Tensor = None):
        """更新指标"""
        self.all_true.extend(y_true.cpu().numpy().tolist())
        self.all_pred.extend(y_pred.cpu().numpy().tolist())
        if y_prob is not None:
            self.all_prob.extend(y_prob.cpu().numpy().tolist())
    
    def compute(self) -> Dict[str, float]:
        """计算最终指标"""
        y_true = np.array(self.all_true)
        y_pred = np.array(self.all_pred)
        y_prob = np.array(self.all_prob) if self.all_prob else None
        
        return calculate_metrics(
            y_true, y_pred, y_prob, 
            self.num_classes, self.dataset_type
        )
    
    def get_primary_metric_value(self) -> float:
        """获取主要指标值"""
        metrics = self.compute()
        return get_primary_metric(metrics, self.dataset_type)


if __name__ == "__main__":
    # 测试指标计算
    print("测试指标计算...")
    
    # 模拟APTOS数据
    y_true_aptos = np.array([0, 1, 2, 3, 4, 0, 1, 2])
    y_pred_aptos = np.array([0, 1, 1, 3, 4, 1, 1, 2])
    y_prob_aptos = np.random.rand(8, 5)
    
    aptos_metrics = calculate_metrics(y_true_aptos, y_pred_aptos, y_prob_aptos, 5, "aptos")
    print("APTOS指标:", aptos_metrics)
    print("APTOS主要指标 (QWK):", get_primary_metric(aptos_metrics, "aptos"))
    
    # 模拟ODIR数据
    y_true_odir = np.array([0, 1, 0, 1, 1, 0])
    y_pred_odir = np.array([0, 1, 1, 1, 0, 0])
    y_prob_odir = np.random.rand(6, 2)
    
    odir_metrics = calculate_metrics(y_true_odir, y_pred_odir, y_prob_odir, 2, "odir")
    print("ODIR指标:", odir_metrics)
    print("ODIR主要指标 (AUC):", get_primary_metric(odir_metrics, "odir"))
    
    print("指标计算测试完成!")
