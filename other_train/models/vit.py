"""
Vision Transformer (ViT) 模型实现
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision import models
import math
from typing import Optional


class PatchEmbedding(nn.Module):
    """图像块嵌入层"""
    
    def __init__(self, img_size: int = 224, patch_size: int = 16, 
                 in_channels: int = 3, embed_dim: int = 768):
        super(PatchEmbedding, self).__init__()
        
        self.img_size = img_size
        self.patch_size = patch_size
        self.num_patches = (img_size // patch_size) ** 2
        
        # 使用卷积层实现patch embedding
        self.projection = nn.Conv2d(
            in_channels, embed_dim, 
            kernel_size=patch_size, stride=patch_size
        )
    
    def forward(self, x):
        # x: (B, C, H, W) -> (B, embed_dim, H//patch_size, W//patch_size)
        x = self.projection(x)
        # (B, embed_dim, H//patch_size, W//patch_size) -> (B, embed_dim, num_patches)
        x = x.flatten(2)
        # (B, embed_dim, num_patches) -> (B, num_patches, embed_dim)
        x = x.transpose(1, 2)
        return x


class MultiHeadSelfAttention(nn.Module):
    """多头自注意力机制"""
    
    def __init__(self, embed_dim: int = 768, num_heads: int = 12, 
                 dropout: float = 0.0, bias: bool = True):
        super(MultiHeadSelfAttention, self).__init__()
        
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        
        assert self.head_dim * num_heads == embed_dim, "embed_dim must be divisible by num_heads"
        
        self.qkv = nn.Linear(embed_dim, embed_dim * 3, bias=bias)
        self.proj = nn.Linear(embed_dim, embed_dim)
        self.dropout = nn.Dropout(dropout)
        
        self.scale = self.head_dim ** -0.5
    
    def forward(self, x):
        B, N, C = x.shape
        
        # 计算Q, K, V
        qkv = self.qkv(x).reshape(B, N, 3, self.num_heads, self.head_dim).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]
        
        # 计算注意力分数
        attn = (q @ k.transpose(-2, -1)) * self.scale
        attn = F.softmax(attn, dim=-1)
        attn = self.dropout(attn)
        
        # 应用注意力权重
        x = (attn @ v).transpose(1, 2).reshape(B, N, C)
        x = self.proj(x)
        x = self.dropout(x)
        
        return x


class MLP(nn.Module):
    """多层感知机"""
    
    def __init__(self, in_features: int, hidden_features: Optional[int] = None,
                 out_features: Optional[int] = None, dropout: float = 0.0):
        super(MLP, self).__init__()
        
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features
        
        self.fc1 = nn.Linear(in_features, hidden_features)
        self.act = nn.GELU()
        self.fc2 = nn.Linear(hidden_features, out_features)
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x):
        x = self.fc1(x)
        x = self.act(x)
        x = self.dropout(x)
        x = self.fc2(x)
        x = self.dropout(x)
        return x


class TransformerBlock(nn.Module):
    """Transformer编码器块"""
    
    def __init__(self, embed_dim: int = 768, num_heads: int = 12,
                 mlp_ratio: float = 4.0, dropout: float = 0.0,
                 attn_dropout: float = 0.0):
        super(TransformerBlock, self).__init__()
        
        self.norm1 = nn.LayerNorm(embed_dim)
        self.attn = MultiHeadSelfAttention(
            embed_dim=embed_dim, 
            num_heads=num_heads, 
            dropout=attn_dropout
        )
        
        self.norm2 = nn.LayerNorm(embed_dim)
        mlp_hidden_dim = int(embed_dim * mlp_ratio)
        self.mlp = MLP(
            in_features=embed_dim,
            hidden_features=mlp_hidden_dim,
            dropout=dropout
        )
    
    def forward(self, x):
        # 自注意力 + 残差连接
        x = x + self.attn(self.norm1(x))
        # MLP + 残差连接
        x = x + self.mlp(self.norm2(x))
        return x


class VisionTransformer(nn.Module):
    """Vision Transformer模型"""
    
    def __init__(self, img_size: int = 224, patch_size: int = 16,
                 in_channels: int = 3, num_classes: int = 1000,
                 embed_dim: int = 768, depth: int = 12, num_heads: int = 12,
                 mlp_ratio: float = 4.0, dropout: float = 0.1,
                 attn_dropout: float = 0.0, pretrained: bool = True):
        super(VisionTransformer, self).__init__()
        
        if pretrained:
            # 使用预训练的ViT模型
            try:
                self.backbone = models.vit_b_16(pretrained=True)
                # 替换分类头
                self.backbone.heads = nn.Linear(self.backbone.hidden_dim, num_classes)
                self.forward = self._forward_pretrained
                return
            except:
                print("警告: 无法加载预训练ViT模型，将从头训练")
                pretrained = False
        
        if not pretrained:
            # 从头构建ViT
            self.num_classes = num_classes
            self.embed_dim = embed_dim
            
            # Patch embedding
            self.patch_embed = PatchEmbedding(
                img_size=img_size, patch_size=patch_size,
                in_channels=in_channels, embed_dim=embed_dim
            )
            num_patches = self.patch_embed.num_patches
            
            # Class token
            self.cls_token = nn.Parameter(torch.zeros(1, 1, embed_dim))
            
            # Position embedding
            self.pos_embed = nn.Parameter(torch.zeros(1, num_patches + 1, embed_dim))
            self.pos_dropout = nn.Dropout(dropout)
            
            # Transformer blocks
            self.blocks = nn.ModuleList([
                TransformerBlock(
                    embed_dim=embed_dim, num_heads=num_heads,
                    mlp_ratio=mlp_ratio, dropout=dropout,
                    attn_dropout=attn_dropout
                )
                for _ in range(depth)
            ])
            
            # Classification head
            self.norm = nn.LayerNorm(embed_dim)
            self.head = nn.Linear(embed_dim, num_classes)
            
            # 初始化权重
            self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化权重"""
        # 初始化position embedding
        nn.init.trunc_normal_(self.pos_embed, std=0.02)
        nn.init.trunc_normal_(self.cls_token, std=0.02)
        
        # 初始化其他层
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.trunc_normal_(m.weight, std=0.02)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)
    
    def _forward_pretrained(self, x):
        """预训练模型的前向传播"""
        return self.backbone(x)
    
    def forward(self, x):
        """前向传播"""
        B = x.shape[0]
        
        # Patch embedding
        x = self.patch_embed(x)  # (B, num_patches, embed_dim)
        
        # 添加class token
        cls_tokens = self.cls_token.expand(B, -1, -1)  # (B, 1, embed_dim)
        x = torch.cat((cls_tokens, x), dim=1)  # (B, num_patches + 1, embed_dim)
        
        # 添加position embedding
        x = x + self.pos_embed
        x = self.pos_dropout(x)
        
        # Transformer blocks
        for block in self.blocks:
            x = block(x)
        
        # 分类
        x = self.norm(x)
        cls_token_final = x[:, 0]  # 取class token
        x = self.head(cls_token_final)
        
        return x


def create_vit(num_classes: int = 1000, pretrained: bool = True,
               patch_size: int = 16, embed_dim: int = 768,
               depth: int = 12, num_heads: int = 12,
               mlp_ratio: float = 4.0, drop_rate: float = 0.1,
               attn_drop_rate: float = 0.0) -> VisionTransformer:
    """
    创建Vision Transformer模型
    
    Args:
        num_classes: 分类数量
        pretrained: 是否使用预训练权重
        patch_size: patch大小
        embed_dim: 嵌入维度
        depth: Transformer层数
        num_heads: 注意力头数
        mlp_ratio: MLP隐藏层倍数
        drop_rate: dropout率
        attn_drop_rate: 注意力dropout率
    
    Returns:
        Vision Transformer模型
    """
    return VisionTransformer(
        patch_size=patch_size,
        num_classes=num_classes,
        embed_dim=embed_dim,
        depth=depth,
        num_heads=num_heads,
        mlp_ratio=mlp_ratio,
        dropout=drop_rate,
        attn_dropout=attn_drop_rate,
        pretrained=pretrained
    )


if __name__ == "__main__":
    # 测试ViT模型
    print("测试Vision Transformer模型...")
    
    # 创建模型
    model = create_vit(num_classes=5, pretrained=False)
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 测试前向传播
    x = torch.randn(2, 3, 224, 224)
    with torch.no_grad():
        output = model(x)
    print(f"输入形状: {x.shape}")
    print(f"输出形状: {output.shape}")
    
    print("Vision Transformer模型测试完成!")
