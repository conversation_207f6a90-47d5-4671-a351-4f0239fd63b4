"""
基础配置类 - 确保所有对比模型使用相同的训练参数
"""
import torch
import random
import numpy as np
from dataclasses import dataclass
from typing import Tuple, List, Optional


@dataclass
class BaseConfig:
    """基础训练配置类，确保实验公平性"""
    
    # 随机种子设置
    random_seed: int = 42
    
    # 训练基础参数
    batch_size: int = 32
    learning_rate: float = 1e-4
    weight_decay: float = 1e-4
    
    # 优化器和调度器
    optimizer_name: str = "Adam"
    scheduler_name: str = "CosineAnnealingLR"
    criterion_name: str = "CrossEntropyLoss"
    
    # 数据预处理参数
    image_size: Tuple[int, int] = (224, 224)
    normalize_mean: Tu<PERSON>[float, float, float] = (0.485, 0.456, 0.406)
    normalize_std: Tuple[float, float, float] = (0.229, 0.224, 0.225)
    
    # 数据增强参数
    rotation_degrees: int = 10
    horizontal_flip_prob: float = 0.5
    vertical_flip_prob: float = 0.2
    color_jitter_brightness: float = 0.1
    color_jitter_contrast: float = 0.1
    color_jitter_saturation: float = 0.1
    color_jitter_hue: float = 0.05
    
    # 硬件设置
    num_workers: int = 4
    pin_memory: bool = True
    
    # 模型保存设置
    save_best_model: bool = True
    save_metrics: bool = True
    results_dir: str = "results"
    models_dir: str = "models_pth"
    
    def __post_init__(self):
        """初始化后设置随机种子和设备"""
        self.set_random_seed()
        self.device = self.get_device()
    
    def set_random_seed(self):
        """设置随机种子确保实验可重复性"""
        random.seed(self.random_seed)
        np.random.seed(self.random_seed)
        torch.manual_seed(self.random_seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(self.random_seed)
            torch.cuda.manual_seed_all(self.random_seed)
        # 确保CUDNN的确定性
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
    
    def get_device(self) -> torch.device:
        """获取计算设备"""
        if torch.cuda.is_available():
            return torch.device('cuda')
        elif torch.backends.mps.is_available():
            return torch.device('mps')
        else:
            return torch.device('cpu')
    
    def get_optimizer(self, model_parameters):
        """获取优化器"""
        if self.optimizer_name == "Adam":
            return torch.optim.Adam(
                model_parameters, 
                lr=self.learning_rate, 
                weight_decay=self.weight_decay
            )
        elif self.optimizer_name == "SGD":
            return torch.optim.SGD(
                model_parameters, 
                lr=self.learning_rate, 
                weight_decay=self.weight_decay,
                momentum=0.9
            )
        else:
            raise ValueError(f"Unsupported optimizer: {self.optimizer_name}")
    
    def get_scheduler(self, optimizer, epochs):
        """获取学习率调度器"""
        if self.scheduler_name == "CosineAnnealingLR":
            return torch.optim.lr_scheduler.CosineAnnealingLR(
                optimizer, T_max=epochs
            )
        elif self.scheduler_name == "StepLR":
            return torch.optim.lr_scheduler.StepLR(
                optimizer, step_size=epochs//3, gamma=0.1
            )
        else:
            raise ValueError(f"Unsupported scheduler: {self.scheduler_name}")
    
    def get_criterion(self):
        """获取损失函数"""
        if self.criterion_name == "CrossEntropyLoss":
            return torch.nn.CrossEntropyLoss()
        elif self.criterion_name == "FocalLoss":
            # 如果需要可以实现FocalLoss
            return torch.nn.CrossEntropyLoss()
        else:
            raise ValueError(f"Unsupported criterion: {self.criterion_name}")
    
    def to_dict(self) -> dict:
        """转换为字典格式，用于保存配置"""
        return {
            'random_seed': self.random_seed,
            'batch_size': self.batch_size,
            'learning_rate': self.learning_rate,
            'weight_decay': self.weight_decay,
            'optimizer': self.optimizer_name,
            'scheduler': self.scheduler_name,
            'criterion': self.criterion_name,
            'image_size': self.image_size,
            'normalize_mean': self.normalize_mean,
            'normalize_std': self.normalize_std,
            'device': str(self.device),
            'num_workers': self.num_workers
        }


@dataclass
class ModelConfig:
    """模型特定配置基类"""
    model_name: str = ""
    pretrained: bool = True
    freeze_backbone: bool = True
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            'model_name': self.model_name,
            'pretrained': self.pretrained,
            'freeze_backbone': self.freeze_backbone
        }


def create_base_config() -> BaseConfig:
    """创建基础配置实例"""
    return BaseConfig()


if __name__ == "__main__":
    # 测试配置类
    config = create_base_config()
    print("基础配置创建成功:")
    print(f"设备: {config.device}")
    print(f"随机种子: {config.random_seed}")
    print(f"批次大小: {config.batch_size}")
    print(f"学习率: {config.learning_rate}")
