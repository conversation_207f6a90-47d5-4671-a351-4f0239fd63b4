"""
训练器模块初始化文件
"""
from .base_trainer import BaseTrainer
from .densenet_trainer import DenseNetTrainer, train_densenet_aptos, train_densenet_odir
from .efficientnet_trainer import EfficientNetTrainer, train_efficientnet_aptos, train_efficientnet_odir
from .vit_trainer import ViTTrainer, train_vit_aptos, train_vit_odir
from .se_resnet_trainer import SEResNetTrainer, train_se_resnet_aptos, train_se_resnet_odir
from .msnets_trainer import MSNetsTrainer, train_msnets_aptos, train_msnets_odir

__all__ = [
    # 基础训练器
    'BaseTrainer',
    
    # 具体训练器
    'DenseNetTrainer', 'EfficientNetTrainer', 'ViTTrainer', 
    'SEResNetTrainer', 'MSNetsTrainer',
    
    # 训练函数
    'train_densenet_aptos', 'train_densenet_odir',
    'train_efficientnet_aptos', 'train_efficientnet_odir',
    'train_vit_aptos', 'train_vit_odir',
    'train_se_resnet_aptos', 'train_se_resnet_odir',
    'train_msnets_aptos', 'train_msnets_odir'
]


def get_trainer_class(model_name: str):
    """
    根据模型名称获取训练器类
    
    Args:
        model_name: 模型名称
    
    Returns:
        训练器类
    """
    trainer_classes = {
        'densenet': DenseNetTrainer,
        'efficientnet': EfficientNetTrainer,
        'vit': ViTTrainer,
        'se_resnet': SEResNetTrainer,
        'msnets': MSNetsTrainer
    }
    
    if model_name.lower() not in trainer_classes:
        raise ValueError(f"Unsupported model: {model_name}")
    
    return trainer_classes[model_name.lower()]


def get_train_function(model_name: str, dataset: str):
    """
    根据模型名称和数据集获取训练函数
    
    Args:
        model_name: 模型名称
        dataset: 数据集名称
    
    Returns:
        训练函数
    """
    train_functions = {
        ('densenet', 'aptos'): train_densenet_aptos,
        ('densenet', 'odir'): train_densenet_odir,
        ('efficientnet', 'aptos'): train_efficientnet_aptos,
        ('efficientnet', 'odir'): train_efficientnet_odir,
        ('vit', 'aptos'): train_vit_aptos,
        ('vit', 'odir'): train_vit_odir,
        ('se_resnet', 'aptos'): train_se_resnet_aptos,
        ('se_resnet', 'odir'): train_se_resnet_odir,
        ('msnets', 'aptos'): train_msnets_aptos,
        ('msnets', 'odir'): train_msnets_odir
    }
    
    key = (model_name.lower(), dataset.lower())
    if key not in train_functions:
        raise ValueError(f"Unsupported combination: {model_name} on {dataset}")
    
    return train_functions[key]


if __name__ == "__main__":
    print("训练器模块初始化完成!")
    print("支持的训练器:")
    for model_name in ['densenet', 'efficientnet', 'vit', 'se_resnet', 'msnets']:
        trainer_class = get_trainer_class(model_name)
        print(f"  - {model_name}: {trainer_class.__name__}")
    
    print("\n支持的训练函数:")
    for model_name in ['densenet', 'efficientnet', 'vit', 'se_resnet', 'msnets']:
        for dataset in ['aptos', 'odir']:
            train_func = get_train_function(model_name, dataset)
            print(f"  - {model_name} on {dataset}: {train_func.__name__}")
