"""
模型模块初始化文件
"""
from .densenet import create_densenet, DenseNet
from .efficientnet import create_efficientnet, EfficientNet
from .vit import create_vit, VisionTransformer
from .se_resnet import create_se_resnet, SEResNet
from .msnets import create_msnets, MSNets

__all__ = [
    # DenseNet
    'create_densenet', 'DenseNet',
    
    # EfficientNet
    'create_efficientnet', 'EfficientNet',
    
    # Vision Transformer
    'create_vit', 'VisionTransformer',
    
    # SE-ResNet
    'create_se_resnet', 'SEResNet',
    
    # MSNets
    'create_msnets', 'MSNets'
]


def get_model_creator(model_name: str):
    """
    根据模型名称获取模型创建函数
    
    Args:
        model_name: 模型名称
    
    Returns:
        模型创建函数
    """
    model_creators = {
        'densenet': create_densenet,
        'efficientnet': create_efficientnet,
        'vit': create_vit,
        'se_resnet': create_se_resnet,
        'msnets': create_msnets
    }
    
    if model_name.lower() not in model_creators:
        raise ValueError(f"Unsupported model: {model_name}")
    
    return model_creators[model_name.lower()]


if __name__ == "__main__":
    print("模型模块初始化完成!")
    print("支持的模型:")
    for model_name in ['densenet', 'efficientnet', 'vit', 'se_resnet', 'msnets']:
        creator = get_model_creator(model_name)
        print(f"  - {model_name}: {creator.__name__}")
