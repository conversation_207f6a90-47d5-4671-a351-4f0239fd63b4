"""
DenseNet模型实现
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision import models
from typing import Tuple


class DenseBlock(nn.Module):
    """DenseNet的Dense Block"""
    
    def __init__(self, num_layers: int, num_input_features: int, 
                 bn_size: int, growth_rate: int, drop_rate: float):
        super(DenseBlock, self).__init__()
        
        self.layers = nn.ModuleList()
        for i in range(num_layers):
            layer = DenseLayer(
                num_input_features + i * growth_rate,
                growth_rate, bn_size, drop_rate
            )
            self.layers.append(layer)
    
    def forward(self, x):
        features = [x]
        for layer in self.layers:
            new_features = layer(torch.cat(features, 1))
            features.append(new_features)
        return torch.cat(features, 1)


class DenseLayer(nn.Module):
    """DenseNet的Dense Layer"""
    
    def __init__(self, num_input_features: int, growth_rate: int, 
                 bn_size: int, drop_rate: float):
        super(DenseLayer, self).__init__()
        
        self.norm1 = nn.BatchNorm2d(num_input_features)
        self.relu1 = nn.ReLU(inplace=True)
        self.conv1 = nn.Conv2d(num_input_features, bn_size * growth_rate,
                              kernel_size=1, stride=1, bias=False)
        
        self.norm2 = nn.BatchNorm2d(bn_size * growth_rate)
        self.relu2 = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv2d(bn_size * growth_rate, growth_rate,
                              kernel_size=3, stride=1, padding=1, bias=False)
        
        self.drop_rate = drop_rate
    
    def forward(self, x):
        out = self.conv1(self.relu1(self.norm1(x)))
        out = self.conv2(self.relu2(self.norm2(out)))
        
        if self.drop_rate > 0:
            out = F.dropout(out, p=self.drop_rate, training=self.training)
        
        return out


class Transition(nn.Module):
    """DenseNet的Transition Layer"""
    
    def __init__(self, num_input_features: int, num_output_features: int):
        super(Transition, self).__init__()
        
        self.norm = nn.BatchNorm2d(num_input_features)
        self.relu = nn.ReLU(inplace=True)
        self.conv = nn.Conv2d(num_input_features, num_output_features,
                             kernel_size=1, stride=1, bias=False)
        self.pool = nn.AvgPool2d(kernel_size=2, stride=2)
    
    def forward(self, x):
        out = self.conv(self.relu(self.norm(x)))
        out = self.pool(out)
        return out


class DenseNet(nn.Module):
    """DenseNet模型"""
    
    def __init__(self, growth_rate: int = 32, block_config: Tuple[int, int, int, int] = (6, 12, 24, 16),
                 num_init_features: int = 64, bn_size: int = 4, drop_rate: float = 0,
                 num_classes: int = 1000, pretrained: bool = True):
        super(DenseNet, self).__init__()
        
        if pretrained:
            # 使用预训练的DenseNet121
            self.backbone = models.densenet121(pretrained=True)
            # 移除分类器
            self.features = self.backbone.features
            num_features = self.backbone.classifier.in_features
        else:
            # 从头构建DenseNet
            # 初始卷积层
            self.features = nn.Sequential()
            self.features.add_module('conv0', nn.Conv2d(3, num_init_features, 
                                                       kernel_size=7, stride=2, padding=3, bias=False))
            self.features.add_module('norm0', nn.BatchNorm2d(num_init_features))
            self.features.add_module('relu0', nn.ReLU(inplace=True))
            self.features.add_module('pool0', nn.MaxPool2d(kernel_size=3, stride=2, padding=1))
            
            # Dense blocks
            num_features = num_init_features
            for i, num_layers in enumerate(block_config):
                block = DenseBlock(num_layers, num_features, bn_size, growth_rate, drop_rate)
                self.features.add_module(f'denseblock{i+1}', block)
                num_features = num_features + num_layers * growth_rate
                
                if i != len(block_config) - 1:
                    trans = Transition(num_features, num_features // 2)
                    self.features.add_module(f'transition{i+1}', trans)
                    num_features = num_features // 2
            
            # 最终的BatchNorm
            self.features.add_module('norm5', nn.BatchNorm2d(num_features))
        
        # 分类器
        self.classifier = nn.Linear(num_features, num_classes)
        
        # 初始化权重
        if not pretrained:
            self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        features = self.features(x)
        out = F.relu(features, inplace=True)
        out = F.adaptive_avg_pool2d(out, (1, 1))
        out = torch.flatten(out, 1)
        out = self.classifier(out)
        return out


def create_densenet(num_classes: int = 1000, pretrained: bool = True, 
                   growth_rate: int = 32, block_config: Tuple[int, int, int, int] = (6, 12, 24, 16),
                   num_init_features: int = 64, bn_size: int = 4, drop_rate: float = 0.0) -> DenseNet:
    """
    创建DenseNet模型
    
    Args:
        num_classes: 分类数量
        pretrained: 是否使用预训练权重
        growth_rate: 增长率
        block_config: 每个dense block的层数
        num_init_features: 初始特征数
        bn_size: bottleneck size
        drop_rate: dropout率
    
    Returns:
        DenseNet模型
    """
    return DenseNet(
        growth_rate=growth_rate,
        block_config=block_config,
        num_init_features=num_init_features,
        bn_size=bn_size,
        drop_rate=drop_rate,
        num_classes=num_classes,
        pretrained=pretrained
    )


if __name__ == "__main__":
    # 测试DenseNet模型
    print("测试DenseNet模型...")
    
    # 创建模型
    model = create_densenet(num_classes=5, pretrained=True)
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 测试前向传播
    x = torch.randn(2, 3, 224, 224)
    with torch.no_grad():
        output = model(x)
    print(f"输入形状: {x.shape}")
    print(f"输出形状: {output.shape}")
    
    print("DenseNet模型测试完成!")
