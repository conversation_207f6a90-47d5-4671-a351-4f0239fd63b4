"""
EfficientNet训练器
"""
import os
import sys
import torch.nn as nn

# 添加路径以导入必要模块
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from other_train.trainers.base_trainer import BaseTrainer
from other_train.models.efficientnet import create_efficientnet
from other_train.config import get_config


class EfficientNetTrainer(BaseTrainer):
    """EfficientNet训练器"""
    
    def __init__(self, config):
        super().__init__(config)
    
    def _create_model(self) -> nn.Module:
        """创建EfficientNet模型"""
        model_config = {
            'num_classes': self.config.num_classes,
            'pretrained': getattr(self.config, 'pretrained', True),
            'compound_coef': getattr(self.config, 'compound_coef', 0),
            'drop_connect_rate': getattr(self.config, 'drop_connect_rate', 0.2)
        }
        
        return create_efficientnet(**model_config)
    
    def _freeze_backbone(self):
        """冻结EfficientNet的backbone"""
        if hasattr(self.model, 'features'):
            # 冻结特征提取层
            for param in self.model.features.parameters():
                param.requires_grad = False
            
            # 只训练分类器
            for param in self.model.classifier.parameters():
                param.requires_grad = True
            
            print("已冻结EfficientNet backbone，只训练分类器")
        elif hasattr(self.model, 'backbone'):
            # 如果使用预训练模型
            for param in self.model.backbone.features.parameters():
                param.requires_grad = False
            
            for param in self.model.classifier.parameters():
                param.requires_grad = True
            
            print("已冻结EfficientNet backbone，只训练分类器")
        else:
            print("警告: 无法找到features层，未执行冻结操作")


def train_efficientnet_aptos():
    """训练EfficientNet在APTOS数据集上"""
    print("=" * 60)
    print("开始训练 EfficientNet 在 APTOS 数据集上")
    print("=" * 60)
    
    # 获取配置
    config = get_config('aptos', 'efficientnet')
    
    # 创建训练器
    trainer = EfficientNetTrainer(config)
    
    # 训练
    trainer.train()
    
    # 测试
    test_metrics = trainer.test()
    print(f"\n测试结果:")
    for metric, value in test_metrics.items():
        print(f"{metric.upper()}: {value:.4f}")
    
    # 保存结果
    trainer.save_training_metrics()
    
    return trainer


def train_efficientnet_odir():
    """训练EfficientNet在ODIR数据集上"""
    print("=" * 60)
    print("开始训练 EfficientNet 在 ODIR 数据集上")
    print("=" * 60)
    
    # 获取配置
    config = get_config('odir', 'efficientnet')
    
    # 创建训练器
    trainer = EfficientNetTrainer(config)
    
    # 训练
    trainer.train()
    
    # 测试
    test_metrics = trainer.test()
    print(f"\n测试结果:")
    for metric, value in test_metrics.items():
        print(f"{metric.upper()}: {value:.4f}")
    
    # 保存结果
    trainer.save_training_metrics()
    
    return trainer


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='训练EfficientNet模型')
    parser.add_argument('--dataset', type=str, choices=['aptos', 'odir'], 
                       default='aptos', help='数据集选择')
    
    args = parser.parse_args()
    
    if args.dataset == 'aptos':
        trainer = train_efficientnet_aptos()
    elif args.dataset == 'odir':
        trainer = train_efficientnet_odir()
    
    print(f"\nEfficientNet在{args.dataset.upper()}数据集上的训练完成!")
