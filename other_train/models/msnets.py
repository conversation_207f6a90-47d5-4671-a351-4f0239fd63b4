"""
MSNets (Multi-Scale Networks) 模型实现
多尺度网络，通过不同尺度的输入来提取多层次特征
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision import models
from typing import List, Optional


class MultiScaleInput(nn.Module):
    """多尺度输入处理模块"""
    
    def __init__(self, scales: List[float] = [1.0, 0.875, 0.75]):
        super(MultiScaleInput, self).__init__()
        self.scales = scales
    
    def forward(self, x):
        """
        对输入图像进行多尺度处理
        Args:
            x: 输入图像 (B, C, H, W)
        Returns:
            多尺度图像列表
        """
        multi_scale_inputs = []
        _, _, h, w = x.shape
        
        for scale in self.scales:
            if scale == 1.0:
                scaled_x = x
            else:
                new_h, new_w = int(h * scale), int(w * scale)
                scaled_x = F.interpolate(x, size=(new_h, new_w), mode='bilinear', align_corners=False)
                # 将缩放后的图像调整回原始尺寸
                scaled_x = F.interpolate(scaled_x, size=(h, w), mode='bilinear', align_corners=False)
            
            multi_scale_inputs.append(scaled_x)
        
        return multi_scale_inputs


class ScaleBranch(nn.Module):
    """单个尺度分支"""
    
    def __init__(self, backbone_type: str = 'resnet50', num_classes: int = 1000, 
                 pretrained: bool = True):
        super(ScaleBranch, self).__init__()
        
        if backbone_type == 'resnet50':
            self.backbone = models.resnet50(pretrained=pretrained)
            self.feature_dim = self.backbone.fc.in_features
            # 移除原始分类器
            self.backbone = nn.Sequential(*list(self.backbone.children())[:-1])
        elif backbone_type == 'resnet34':
            self.backbone = models.resnet34(pretrained=pretrained)
            self.feature_dim = self.backbone.fc.in_features
            self.backbone = nn.Sequential(*list(self.backbone.children())[:-1])
        elif backbone_type == 'densenet121':
            self.backbone = models.densenet121(pretrained=pretrained)
            self.feature_dim = self.backbone.classifier.in_features
            # 移除分类器，保留特征提取部分
            self.backbone = self.backbone.features
        else:
            raise ValueError(f"Unsupported backbone type: {backbone_type}")
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool2d(1)
        
        # 特征投影层
        self.feature_proj = nn.Sequential(
            nn.Linear(self.feature_dim, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5)
        )
    
    def forward(self, x):
        """前向传播"""
        features = self.backbone(x)
        
        # 如果是DenseNet，需要额外处理
        if isinstance(self.backbone, nn.Sequential) and hasattr(self.backbone[-1], 'norm5'):
            features = F.relu(features, inplace=True)
        
        # 全局平均池化
        features = self.global_pool(features)
        features = torch.flatten(features, 1)
        
        # 特征投影
        features = self.feature_proj(features)
        
        return features


class FeatureFusion(nn.Module):
    """特征融合模块"""
    
    def __init__(self, input_dim: int, num_scales: int, fusion_method: str = 'concat'):
        super(FeatureFusion, self).__init__()
        
        self.fusion_method = fusion_method
        self.num_scales = num_scales
        
        if fusion_method == 'concat':
            # 简单拼接
            self.fusion_dim = input_dim * num_scales
            self.fusion_layer = nn.Identity()
        elif fusion_method == 'attention':
            # 注意力融合
            self.fusion_dim = input_dim
            self.attention_weights = nn.Sequential(
                nn.Linear(input_dim, input_dim // 4),
                nn.ReLU(inplace=True),
                nn.Linear(input_dim // 4, 1),
                nn.Sigmoid()
            )
        elif fusion_method == 'weighted_sum':
            # 加权求和
            self.fusion_dim = input_dim
            self.scale_weights = nn.Parameter(torch.ones(num_scales) / num_scales)
        else:
            raise ValueError(f"Unsupported fusion method: {fusion_method}")
    
    def forward(self, multi_scale_features):
        """
        融合多尺度特征
        Args:
            multi_scale_features: 多尺度特征列表 [(B, D), (B, D), ...]
        Returns:
            融合后的特征 (B, fusion_dim)
        """
        if self.fusion_method == 'concat':
            # 简单拼接
            fused_features = torch.cat(multi_scale_features, dim=1)
        
        elif self.fusion_method == 'attention':
            # 注意力融合
            attention_scores = []
            for features in multi_scale_features:
                score = self.attention_weights(features)
                attention_scores.append(score)
            
            # 归一化注意力权重
            attention_scores = torch.stack(attention_scores, dim=1)  # (B, num_scales, 1)
            attention_scores = F.softmax(attention_scores, dim=1)
            
            # 加权求和
            stacked_features = torch.stack(multi_scale_features, dim=1)  # (B, num_scales, D)
            fused_features = torch.sum(stacked_features * attention_scores, dim=1)
        
        elif self.fusion_method == 'weighted_sum':
            # 学习权重的加权求和
            weights = F.softmax(self.scale_weights, dim=0)
            stacked_features = torch.stack(multi_scale_features, dim=1)  # (B, num_scales, D)
            fused_features = torch.sum(stacked_features * weights.view(1, -1, 1), dim=1)
        
        return fused_features


class MSNets(nn.Module):
    """Multi-Scale Networks"""
    
    def __init__(self, num_classes: int = 1000, scales: List[float] = [1.0, 0.875, 0.75],
                 backbone_type: str = 'resnet50', fusion_method: str = 'concat',
                 pretrained: bool = True):
        super(MSNets, self).__init__()
        
        self.scales = scales
        self.num_scales = len(scales)
        
        # 多尺度输入处理
        self.multi_scale_input = MultiScaleInput(scales)
        
        # 为每个尺度创建分支（共享权重）
        self.scale_branch = ScaleBranch(
            backbone_type=backbone_type,
            num_classes=num_classes,
            pretrained=pretrained
        )
        
        # 特征融合
        self.feature_fusion = FeatureFusion(
            input_dim=512,  # ScaleBranch的输出维度
            num_scales=self.num_scales,
            fusion_method=fusion_method
        )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(self.feature_fusion.fusion_dim, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(256, num_classes)
        )
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        """前向传播"""
        # 生成多尺度输入
        multi_scale_inputs = self.multi_scale_input(x)
        
        # 提取多尺度特征
        multi_scale_features = []
        for scale_input in multi_scale_inputs:
            features = self.scale_branch(scale_input)
            multi_scale_features.append(features)
        
        # 融合特征
        fused_features = self.feature_fusion(multi_scale_features)
        
        # 分类
        output = self.classifier(fused_features)
        
        return output


def create_msnets(num_classes: int = 1000, scales: List[float] = [1.0, 0.875, 0.75],
                 backbone_type: str = 'resnet50', fusion_method: str = 'concat',
                 pretrained: bool = True) -> MSNets:
    """
    创建MSNets模型
    
    Args:
        num_classes: 分类数量
        scales: 多尺度比例列表
        backbone_type: 骨干网络类型
        fusion_method: 特征融合方法 ('concat', 'attention', 'weighted_sum')
        pretrained: 是否使用预训练权重
    
    Returns:
        MSNets模型
    """
    return MSNets(
        num_classes=num_classes,
        scales=scales,
        backbone_type=backbone_type,
        fusion_method=fusion_method,
        pretrained=pretrained
    )


if __name__ == "__main__":
    # 测试MSNets模型
    print("测试MSNets模型...")
    
    # 创建模型
    model = create_msnets(
        num_classes=5, 
        scales=[1.0, 0.875, 0.75],
        backbone_type='resnet50',
        fusion_method='concat',
        pretrained=False
    )
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 测试前向传播
    x = torch.randn(2, 3, 224, 224)
    with torch.no_grad():
        output = model(x)
    print(f"输入形状: {x.shape}")
    print(f"输出形状: {output.shape}")
    
    # 测试不同融合方法
    for fusion_method in ['concat', 'attention', 'weighted_sum']:
        model_test = create_msnets(
            num_classes=5,
            fusion_method=fusion_method,
            pretrained=False
        )
        with torch.no_grad():
            output_test = model_test(x)
        print(f"融合方法 {fusion_method} - 输出形状: {output_test.shape}")
    
    print("MSNets模型测试完成!")
