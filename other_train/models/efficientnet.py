"""
EfficientNet模型实现
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision import models
import math
from typing import Optional, List


class MBConvBlock(nn.Module):
    """Mobile Inverted Bottleneck Convolution Block"""
    
    def __init__(self, in_channels: int, out_channels: int, kernel_size: int,
                 stride: int, expand_ratio: int, se_ratio: float = 0.25,
                 drop_connect_rate: float = 0.0):
        super(MBConvBlock, self).__init__()
        
        self.drop_connect_rate = drop_connect_rate
        self.use_residual = stride == 1 and in_channels == out_channels
        
        # Expansion phase
        expanded_channels = in_channels * expand_ratio
        if expand_ratio != 1:
            self.expand_conv = nn.Conv2d(in_channels, expanded_channels, 1, bias=False)
            self.expand_bn = nn.BatchNorm2d(expanded_channels)
        else:
            self.expand_conv = None
        
        # Depthwise convolution
        self.depthwise_conv = nn.Conv2d(
            expanded_channels, expanded_channels, kernel_size,
            stride=stride, padding=kernel_size//2, groups=expanded_channels, bias=False
        )
        self.depthwise_bn = nn.BatchNorm2d(expanded_channels)
        
        # Squeeze and Excitation
        if se_ratio > 0:
            se_channels = max(1, int(in_channels * se_ratio))
            self.se_reduce = nn.Conv2d(expanded_channels, se_channels, 1)
            self.se_expand = nn.Conv2d(se_channels, expanded_channels, 1)
        else:
            self.se_reduce = None
            self.se_expand = None
        
        # Output phase
        self.project_conv = nn.Conv2d(expanded_channels, out_channels, 1, bias=False)
        self.project_bn = nn.BatchNorm2d(out_channels)
        
        self.swish = nn.SiLU()
    
    def forward(self, x):
        identity = x
        
        # Expansion
        if self.expand_conv is not None:
            x = self.swish(self.expand_bn(self.expand_conv(x)))
        
        # Depthwise convolution
        x = self.swish(self.depthwise_bn(self.depthwise_conv(x)))
        
        # Squeeze and Excitation
        if self.se_reduce is not None:
            se = F.adaptive_avg_pool2d(x, 1)
            se = self.swish(self.se_reduce(se))
            se = torch.sigmoid(self.se_expand(se))
            x = x * se
        
        # Output projection
        x = self.project_bn(self.project_conv(x))
        
        # Skip connection and drop connect
        if self.use_residual:
            if self.drop_connect_rate > 0 and self.training:
                x = self._drop_connect(x, self.drop_connect_rate)
            x = x + identity
        
        return x
    
    def _drop_connect(self, x, drop_connect_rate):
        """Drop connect implementation"""
        if not self.training:
            return x
        
        keep_prob = 1.0 - drop_connect_rate
        random_tensor = keep_prob + torch.rand((x.size(0), 1, 1, 1), 
                                              dtype=x.dtype, device=x.device)
        binary_tensor = torch.floor(random_tensor)
        return x / keep_prob * binary_tensor


class EfficientNet(nn.Module):
    """EfficientNet模型"""
    
    def __init__(self, width_mult: float = 1.0, depth_mult: float = 1.0,
                 dropout_rate: float = 0.2, drop_connect_rate: float = 0.2,
                 num_classes: int = 1000, pretrained: bool = True):
        super(EfficientNet, self).__init__()
        
        if pretrained:
            # 使用预训练的EfficientNet-B0
            self.backbone = models.efficientnet_b0(pretrained=True)
            # 获取特征提取器
            self.features = self.backbone.features
            # 获取分类器输入特征数
            num_features = self.backbone.classifier[1].in_features
        else:
            # 从头构建EfficientNet
            # 基础配置 (EfficientNet-B0)
            base_config = [
                # (expand_ratio, channels, repeats, stride, kernel_size)
                (1, 16, 1, 1, 3),
                (6, 24, 2, 2, 3),
                (6, 40, 2, 2, 5),
                (6, 80, 3, 2, 3),
                (6, 112, 3, 1, 5),
                (6, 192, 4, 2, 5),
                (6, 320, 1, 1, 3),
            ]
            
            # 应用缩放因子
            scaled_config = []
            for expand_ratio, channels, repeats, stride, kernel_size in base_config:
                scaled_channels = int(channels * width_mult)
                scaled_repeats = int(math.ceil(repeats * depth_mult))
                scaled_config.append((expand_ratio, scaled_channels, scaled_repeats, stride, kernel_size))
            
            # 构建特征提取器
            self.features = nn.Sequential()
            
            # Stem
            stem_channels = int(32 * width_mult)
            self.features.add_module('stem', nn.Sequential(
                nn.Conv2d(3, stem_channels, 3, stride=2, padding=1, bias=False),
                nn.BatchNorm2d(stem_channels),
                nn.SiLU()
            ))
            
            # MBConv blocks
            in_channels = stem_channels
            total_blocks = sum(repeats for _, _, repeats, _, _ in scaled_config)
            block_idx = 0
            
            for stage_idx, (expand_ratio, out_channels, repeats, stride, kernel_size) in enumerate(scaled_config):
                for block_idx_in_stage in range(repeats):
                    block_stride = stride if block_idx_in_stage == 0 else 1
                    block_drop_rate = drop_connect_rate * block_idx / total_blocks
                    
                    block = MBConvBlock(
                        in_channels, out_channels, kernel_size,
                        block_stride, expand_ratio, drop_connect_rate=block_drop_rate
                    )
                    
                    self.features.add_module(f'block_{stage_idx}_{block_idx_in_stage}', block)
                    in_channels = out_channels
                    block_idx += 1
            
            # Head
            head_channels = int(1280 * width_mult)
            self.features.add_module('head', nn.Sequential(
                nn.Conv2d(in_channels, head_channels, 1, bias=False),
                nn.BatchNorm2d(head_channels),
                nn.SiLU()
            ))
            
            num_features = head_channels
        
        # 分类器
        self.avgpool = nn.AdaptiveAvgPool2d(1)
        self.dropout = nn.Dropout(dropout_rate)
        self.classifier = nn.Linear(num_features, num_classes)
        
        # 初始化权重
        if not pretrained:
            self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        x = self.features(x)
        x = self.avgpool(x)
        x = torch.flatten(x, 1)
        x = self.dropout(x)
        x = self.classifier(x)
        return x


def create_efficientnet(num_classes: int = 1000, pretrained: bool = True,
                       compound_coef: int = 0, drop_connect_rate: float = 0.2) -> EfficientNet:
    """
    创建EfficientNet模型
    
    Args:
        num_classes: 分类数量
        pretrained: 是否使用预训练权重
        compound_coef: 复合缩放系数 (0-7 对应 B0-B7)
        drop_connect_rate: Drop connect率
    
    Returns:
        EfficientNet模型
    """
    # EfficientNet缩放参数
    phi_values = {
        0: (1.0, 1.0, 0.2),  # B0: (width, depth, dropout)
        1: (1.0, 1.1, 0.2),  # B1
        2: (1.1, 1.2, 0.3),  # B2
        3: (1.2, 1.4, 0.3),  # B3
        4: (1.4, 1.8, 0.4),  # B4
        5: (1.6, 2.2, 0.4),  # B5
        6: (1.8, 2.6, 0.5),  # B6
        7: (2.0, 3.1, 0.5),  # B7
    }
    
    width_mult, depth_mult, dropout_rate = phi_values.get(compound_coef, phi_values[0])
    
    return EfficientNet(
        width_mult=width_mult,
        depth_mult=depth_mult,
        dropout_rate=dropout_rate,
        drop_connect_rate=drop_connect_rate,
        num_classes=num_classes,
        pretrained=pretrained
    )


if __name__ == "__main__":
    # 测试EfficientNet模型
    print("测试EfficientNet模型...")
    
    # 创建模型
    model = create_efficientnet(num_classes=5, pretrained=True, compound_coef=0)
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 测试前向传播
    x = torch.randn(2, 3, 224, 224)
    with torch.no_grad():
        output = model(x)
    print(f"输入形状: {x.shape}")
    print(f"输出形状: {output.shape}")
    
    print("EfficientNet模型测试完成!")
