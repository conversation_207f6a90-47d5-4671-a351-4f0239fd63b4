"""
APTOS数据集专用配置
"""
from dataclasses import dataclass
from .base_config import BaseConfig, ModelConfig


@dataclass
class APTOSConfig(BaseConfig):
    """APTOS数据集配置类"""
    
    # 数据集特定参数
    dataset_name: str = "APTOS"
    num_classes: int = 5
    epochs: int = 15
    
    # APTOS数据集路径
    data_dir: str = "./data/APTOS"
    
    # APTOS主要评估指标
    primary_metric: str = "qwk"  # Quadratic Weighted Kappa
    secondary_metrics: list = None
    
    def __post_init__(self):
        super().__post_init__()
        if self.secondary_metrics is None:
            self.secondary_metrics = ["accuracy", "auc", "precision", "recall", "f1"]
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        base_dict = super().to_dict()
        base_dict.update({
            'dataset_name': self.dataset_name,
            'num_classes': self.num_classes,
            'epochs': self.epochs,
            'data_dir': self.data_dir,
            'primary_metric': self.primary_metric,
            'secondary_metrics': self.secondary_metrics
        })
        return base_dict


@dataclass
class APTOSDenseNetConfig(APTOSConfig):
    """APTOS DenseNet配置"""
    model_name: str = "DenseNet121"
    growth_rate: int = 32
    block_config: tuple = (6, 12, 24, 16)
    num_init_features: int = 64
    bn_size: int = 4
    drop_rate: float = 0.0


@dataclass
class APTOSEfficientNetConfig(APTOSConfig):
    """APTOS EfficientNet配置"""
    model_name: str = "EfficientNet-B0"
    compound_coef: int = 0
    drop_connect_rate: float = 0.2


@dataclass
class APTOSViTConfig(APTOSConfig):
    """APTOS Vision Transformer配置"""
    model_name: str = "ViT-B/16"
    patch_size: int = 16
    embed_dim: int = 768
    depth: int = 12
    num_heads: int = 12
    mlp_ratio: float = 4.0
    drop_rate: float = 0.1
    attn_drop_rate: float = 0.0


@dataclass
class APTOSSEResNetConfig(APTOSConfig):
    """APTOS SE-ResNet配置"""
    model_name: str = "SE-ResNet50"
    layers: tuple = (3, 4, 6, 3)
    reduction: int = 16


@dataclass
class APTOSMSNetsConfig(APTOSConfig):
    """APTOS MSNets配置"""
    model_name: str = "MSNets"
    scales: list = None
    fusion_method: str = "concat"
    
    def __post_init__(self):
        super().__post_init__()
        if self.scales is None:
            self.scales = [1.0, 0.875, 0.75]


def create_aptos_config(model_type: str = "base") -> APTOSConfig:
    """创建APTOS配置实例"""
    config_map = {
        "base": APTOSConfig,
        "densenet": APTOSDenseNetConfig,
        "efficientnet": APTOSEfficientNetConfig,
        "vit": APTOSViTConfig,
        "se_resnet": APTOSSEResNetConfig,
        "msnets": APTOSMSNetsConfig
    }
    
    if model_type not in config_map:
        raise ValueError(f"Unsupported model type: {model_type}")
    
    return config_map[model_type]()


if __name__ == "__main__":
    # 测试APTOS配置
    config = create_aptos_config("base")
    print("APTOS基础配置:")
    print(f"数据集: {config.dataset_name}")
    print(f"类别数: {config.num_classes}")
    print(f"训练轮数: {config.epochs}")
    print(f"主要指标: {config.primary_metric}")
    
    # 测试DenseNet配置
    densenet_config = create_aptos_config("densenet")
    print(f"\nDenseNet配置:")
    print(f"模型名称: {densenet_config.model_name}")
    print(f"增长率: {densenet_config.growth_rate}")
